# Flexible Email Engine Configuration
# Each engine in the array has a nameKey for external calls and a type to determine the engine

[contact]
defaultEmail = "<EMAIL>"
defaultEmailFromName = "Your Company"
defaultRTEmail = "<EMAIL>"
defaultReciveEmail = "<EMAIL>"
devEmails = "<EMAIL>"

[dbs]
verbose = 3

[dbs.tmp]
uri = "mongodb://localhost:27017/test"

[golog]
dir = "/tmp/logs"
level = "info"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"

[preDefColls.emailMXFailed]
collName = "email_mx_failed"
dbName = "tmp"

[preDefColls.emailWhitelist]
collName = "email_whitelist"
dbName = "tmp"

[preDefColls.mailLog]
collName = "mail_log"
dbName = "tmp"

  [preDefColls.mailLog.options]
  expireAfterSeconds = 2_592_000

    [preDefColls.mailLog.options.timeseries]
    metaField = "metadata"
    timeField = "timestamp"

# Email Engine Array Configuration
[[emailEngine]]
nameKey = "gmail-primary"           # External call identifier
type = "smtp"                       # Engine type
enabled = true
defaultEmail = "<EMAIL>"
priority = 1                        # Lower number = higher priority
rateLimit = 250                     # Interval in milliseconds
# SMTP specific fields
host = "smtp.gmail.com"
port = 587
user = "<EMAIL>"
password = "your-app-password"
tls = true
startTLS = true

[[emailEngine]]
nameKey = "gmail-secondary"
type = "smtp"
enabled = true
defaultEmail = "<EMAIL>"
priority = 2
rateLimit = 300
# SMTP specific fields
host = "smtp.gmail.com"
port = 587
user = "<EMAIL>"
password = "your-app-password-2"
tls = true
startTLS = true

[[emailEngine]]
nameKey = "outlook-business"
type = "smtp"
enabled = true
defaultEmail = "<EMAIL>"
priority = 1
rateLimit = 250
# SMTP specific fields
host = "smtp-mail.outlook.com"
port = 587
user = "<EMAIL>"
password = "your-password"
tls = true
startTLS = true

[[emailEngine]]
nameKey = "qq-mail"
type = "smtp"
enabled = true
defaultEmail = "<EMAIL>"
priority = 3
rateLimit = 500
# SMTP specific fields
host = "smtp.qq.com"
port = 587
user = "<EMAIL>"
password = "your-authorization-code"
tls = true
startTLS = true

[[emailEngine]]
nameKey = "company-smtp"
type = "smtp"
enabled = true
defaultEmail = "<EMAIL>"
priority = 2
rateLimit = 200
# SMTP specific fields
host = "mail.company.com"
port = 465
user = "<EMAIL>"
password = "company-password"
tls = true
startTLS = false
ssl = true

[[emailEngine]]
nameKey = "aws-ses-primary"
type = "ses"                        # Different engine type
enabled = true
defaultEmail = "<EMAIL>"
priority = 1
rateLimit = 100
# SES specific fields
region = "us-east-1"
accessKeyId = "********************"
secretAccessKey = "Uogp6eMqmbIhxB43mqCYPJYBT9x3vnQZwrawvz6W"
endpoint = ""                       # Optional custom endpoint

[[emailEngine]]
nameKey = "aws-ses-backup"
type = "ses"
enabled = true
defaultEmail = "<EMAIL>"
priority = 4
rateLimit = 150
# SES specific fields
region = "us-west-2"
accessKeyId = "********************"
secretAccessKey = "NqYJDdVhIMdvVqlJnmBSIBsbHKCEQLlkw91/v4rA"
endpoint = ""

[[emailEngine]]
nameKey = "rm-mail-service"
type = "rmMail"                     # Custom service type
enabled = true
defaultEmail = "<EMAIL>"
priority = 2
rateLimit = 300
# RMMail specific fields
url = "https://ml1.realmaster.cc/send"
apiKey = "your-api-key"
timeout = 30

[[emailEngine]]
nameKey = "sendgrid-marketing"
type = "sendgrid"                   # SendGrid type
enabled = false                     # Disabled
defaultEmail = "<EMAIL>"
priority = 3
rateLimit = 200
# SendGrid specific fields
apiKey = "*********************************************************************"
templateEngine = true

[[emailEngine]]
nameKey = "local-sendmail"
type = "sendmail"                   # System sendmail
enabled = true
defaultEmail = "system@localhost"
priority = 5
rateLimit = 1000
# Sendmail specific fields
path = "/usr/sbin/sendmail"
args = ["-t", "-i"]

[[emailEngine]]
nameKey = "mock-testing"
type = "mock"                       # Mock for testing
enabled = false
defaultEmail = "<EMAIL>"
priority = 10
rateLimit = 0
# Mock specific fields
verbose = true
logFile = "/tmp/mock-emails.log"
saveToFile = true

# Default engine selection strategy
[emailEngineConfig]
defaultEngine = "gmail-primary"     # Default engine nameKey
fallbackStrategy = "priority"       # "priority", "roundrobin", "random"
retryCount = 3
retryDelay = 1000                   # milliseconds
enableFailover = true
healthCheckInterval = 300           # seconds
