# CA6/CA7 分离下载方案

## 1. 需求分析

### 1.1 当前架构问题
- **存储配置**: 每个board类型配置了两个存储路径，分别对应ca6和ca7两个盘
- **下载逻辑**: 当前下载器会同时写入所有配置的存储路径（冗余存储）
- **队列共享**: ca6和ca7使用同一个MongoDB队列，会导致重复处理
- **数据库冲突**: 两台服务器会同时更新同一个merged表，造成数据竞争

### 1.2 核心问题
1. **队列竞争**: 两台服务器从同一个队列取任务，可能重复处理同一个property
2. **数据库写入冲突**: 同时更新merged表的phoLH、docLH、tnLH字段会产生冲突
3. **文件存储重复**: 当前设计会在两个盘都写入文件，造成存储浪费

### 1.3 目标需求
- **ca6服务器**: 只处理分配给ca6的任务，只下载到ca6路径
- **ca7服务器**: 只处理分配给ca7的任务，只下载到ca7路径
- **避免冲突**: 确保同一个property只被一台服务器处理
- **数据一致性**: 保证merged表数据的完整性和一致性

## 2. 技术方案

### 2.1 方案选择
考虑到队列竞争和数据库冲突问题，有以下几种方案：

**方案A: 队列分片** - 根据property ID哈希分配到不同队列
**方案B: 主机标识过滤** - 在队列处理时根据主机名过滤任务
**方案C: 负载均衡分配** - 使用一致性哈希算法分配任务

**推荐方案B**: 主机标识过滤，实现简单，影响最小

### 2.2 核心实现思路
1. **队列任务过滤**: 在GetNext时根据property ID和主机名决定是否处理
2. **存储路径过滤**: 根据主机名只使用对应的存储路径
3. **一致性保证**: 确保同一property始终由同一台服务器处理
4. **向后兼容**: 非ca6/ca7主机保持原有行为

## 3. 实现方案

### 3.1 修改文件清单

#### 3.1.1 核心修改文件
- `goresodownload/resource_download_queue.go` - 修改GetNext函数，添加主机过滤
- `golevelstore/level_store_helper.go` - 修改GetImageDir函数，过滤存储路径
- `goresodownload/host_assignment.go` - 新增主机任务分配逻辑（新文件）

#### 3.1.2 配置文件
- 生产环境配置文件 - 无需修改，保持现有配置

### 3.2 详细实现步骤

#### 步骤1: 创建主机任务分配模块
创建新文件 `goresodownload/host_assignment.go`:

```go
package goresodownload

import (
    "crypto/md5"
    "fmt"
    "os"
    "strings"
    golog "github.com/real-rm/golog"
)

// HostAssignment 主机任务分配器
type HostAssignment struct {
    hostname string
    hostList []string
}

// NewHostAssignment 创建主机任务分配器
func NewHostAssignment() *HostAssignment {
    hostname, err := os.Hostname()
    if err != nil {
        golog.Warn("Failed to get hostname, using empty hostname", "error", err)
        hostname = ""
    }

    // 定义参与分配的主机列表
    hostList := []string{"ca6", "ca7"}

    return &HostAssignment{
        hostname: hostname,
        hostList: hostList,
    }
}

// ShouldProcessProperty 判断当前主机是否应该处理指定的property
func (ha *HostAssignment) ShouldProcessProperty(propertyID string) bool {
    // 如果不是ca6/ca7主机，处理所有任务（保持兼容性）
    if !ha.isTargetHost() {
        return true
    }

    // 使用一致性哈希分配任务
    assignedHost := ha.getAssignedHost(propertyID)
    shouldProcess := assignedHost == ha.hostname

    golog.Debug("Property assignment check",
        "propertyID", propertyID,
        "currentHost", ha.hostname,
        "assignedHost", assignedHost,
        "shouldProcess", shouldProcess)

    return shouldProcess
}

// isTargetHost 检查是否为目标主机
func (ha *HostAssignment) isTargetHost() bool {
    for _, host := range ha.hostList {
        if ha.hostname == host {
            return true
        }
    }
    return false
}

// getAssignedHost 使用哈希算法确定property应该分配给哪个主机
func (ha *HostAssignment) getAssignedHost(propertyID string) string {
    if len(ha.hostList) == 0 {
        return ""
    }

    // 使用MD5哈希确保分配的一致性
    hash := md5.Sum([]byte(propertyID))
    hashValue := int(hash[0]) // 使用第一个字节

    // 取模分配到主机
    hostIndex := hashValue % len(ha.hostList)
    return ha.hostList[hostIndex]
}

// FilterStoragePaths 根据主机名过滤存储路径
func (ha *HostAssignment) FilterStoragePaths(paths []string) []string {
    if !ha.isTargetHost() {
        return paths // 非ca6/ca7主机，返回所有路径
    }

    // 过滤路径
    var filteredPaths []string
    for _, path := range paths {
        if strings.Contains(path, ha.hostname) {
            filteredPaths = append(filteredPaths, path)
        }
    }

    if len(filteredPaths) == 0 {
        golog.Warn("No matching storage paths found for host",
            "hostname", ha.hostname,
            "originalPaths", paths)
        return paths // 如果没有匹配的路径，返回原始路径避免服务异常
    }

    golog.Info("Filtered storage paths for host",
        "hostname", ha.hostname,
        "originalCount", len(paths),
        "filteredCount", len(filteredPaths),
        "filteredPaths", filteredPaths)

    return filteredPaths
}
```

#### 步骤2: 修改队列GetNext函数
在 `goresodownload/resource_download_queue.go` 中修改GetNext函数:

```go
// 在文件顶部添加全局分配器变量
var globalHostAssignment *HostAssignment

func init() {
    // 初始化主机任务分配器
    globalHostAssignment = NewHostAssignment()
}

// 修改GetNext函数，添加主机过滤逻辑
func (q *ResourceDownloadQueue) GetNext(boardType string) (*QueueItem, error) {
    ctx := context.Background()
    maxRetries := 10 // 最大重试次数，避免无限循环

    for retry := 0; retry < maxRetries; retry++ {
        // Filter: items with dlShallEndTs in the past and matching boardType
        filter := bson.M{
            "src":          boardType,
            "dlShallEndTs": bson.M{"$lt": time.Now()},
        }

        // Update: set dlShallEndTs to current time + DOWNLOAD_ALLOWED_MS
        newDlShallEndTs := time.Now().Add(time.Duration(DOWNLOAD_ALLOWED_MS) * time.Millisecond)
        update := bson.M{
            "$set": bson.M{
                "dlShallEndTs": newDlShallEndTs,
            },
        }

        // Options: sort by priority descending (highest priority first)
        opts := options.FindOneAndUpdate().
            SetSort(bson.M{"priority": -1}).
            SetReturnDocument(options.After)

        var result QueueItem
        err := q.queueCol.FindOneAndUpdate(ctx, filter, update, opts).Decode(&result)
        if err != nil {
            if err == mongo.ErrNoDocuments {
                return nil, nil // No items available
            }
            return nil, fmt.Errorf("failed to find and update queue item: %w", err)
        }

        // 检查当前主机是否应该处理这个property
        if globalHostAssignment != nil && !globalHostAssignment.ShouldProcessProperty(result.ID) {
            golog.Debug("Skipping property not assigned to this host",
                "propertyID", result.ID,
                "hostname", globalHostAssignment.hostname)

            // 重置dlShallEndTs，让其他主机可以处理
            resetUpdate := bson.M{
                "$set": bson.M{
                    "dlShallEndTs": time.Now().Add(-time.Hour), // 设置为过去时间
                },
            }
            q.queueCol.UpdateOne(ctx, bson.M{"_id": result.ID}, resetUpdate)
            continue // 继续寻找下一个任务
        }

        return &result, nil
    }

    // 达到最大重试次数，返回空
    golog.Debug("Reached max retries in GetNext, no suitable tasks found")
    return nil, nil
}
```

#### 步骤3: 修改GetImageDir函数
在 `golevelstore/level_store_helper.go` 中修改GetImageDir函数:

```go
// 修改GetImageDir函数，在返回前应用路径过滤
var GetImageDir = func(board string) ([]string, error) {
    // ... 现有的获取路径逻辑保持不变 ...

    // 在返回前应用主机路径过滤
    if len(dirs) > 0 {
        // 导入goresodownload包以使用HostAssignment
        if hostAssignment := goresodownload.NewHostAssignment(); hostAssignment != nil {
            dirs = hostAssignment.FilterStoragePaths(dirs)
        }
    }

    golog.Info("GetImageDir", "board", board, "dirs", dirs)
    return dirs, nil
}
```

### 3.3 测试验证

#### 3.3.1 单元测试
创建 `golevelstore/host_filter_test.go`:

```go
package levelStore

import (
    "testing"
    "github.com/stretchr/testify/assert"
)

func TestHostStorageFilter_FilterStoragePaths(t *testing.T) {
    tests := []struct {
        name     string
        hostname string
        paths    []string
        expected []string
    }{
        {
            name:     "ca6 host filters ca6 paths",
            hostname: "ca6",
            paths:    []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
            expected: []string{"/mnt/ca6m0/imgs/MLS/TRB"},
        },
        {
            name:     "ca7 host filters ca7 paths", 
            hostname: "ca7",
            paths:    []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
            expected: []string{"/mnt/ca7m0/imgs/MLS/TRB"},
        },
        {
            name:     "other host returns all paths",
            hostname: "other",
            paths:    []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
            expected: []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            filter := &HostStorageFilter{hostname: tt.hostname}
            result := filter.FilterStoragePaths(tt.paths)
            assert.Equal(t, tt.expected, result)
        })
    }
}
```

## 4. 部署方案

### 4.1 部署步骤
1. **代码部署**: 将修改后的代码部署到ca6和ca7服务器
2. **服务重启**: 重启goresodownload服务使新配置生效
3. **验证测试**: 检查日志确认路径过滤正常工作

### 4.2 验证方法
1. **日志检查**: 查看启动日志中的"Filtered storage paths for host"信息
2. **功能测试**: 运行下载任务，确认只写入对应主机的存储路径
3. **监控观察**: 观察磁盘使用情况，确认分离效果

### 4.3 回滚方案
如果出现问题，可以快速回滚到原始代码版本，恢复双路径写入模式。

## 5. 风险评估

### 5.1 低风险
- **向后兼容**: 非ca6/ca7主机行为不变
- **配置不变**: 无需修改现有配置文件
- **渐进部署**: 可以逐台服务器部署验证

### 5.2 注意事项
- **主机名依赖**: 确保ca6/ca7服务器主机名设置正确
- **路径匹配**: 确保存储路径包含正确的ca6/ca7标识
- **日志监控**: 部署后需要监控过滤日志确认正常工作

## 6. 后续优化

### 6.1 可选增强
- **配置化映射**: 将主机名到路径的映射关系配置化
- **更灵活的过滤规则**: 支持正则表达式或更复杂的过滤逻辑
- **监控指标**: 添加存储路径使用的监控指标

### 6.2 长期规划
- **存储策略优化**: 考虑基于负载均衡的智能存储分配
- **容灾机制**: 在单个存储路径不可用时的自动切换机制
