# CA6/CA7 分离下载方案

## 1. 需求分析

### 1.1 当前架构
- **存储配置**: 每个board类型配置了两个存储路径，分别对应ca6和ca7两个盘
- **下载逻辑**: 当前下载器会同时写入所有配置的存储路径（冗余存储）
- **配置示例**:
```ini
[imageStore]
reso_treb_image_dir = ["/mnt/ca6m0/imgs/MLS/TRB","/mnt/ca7m0/imgs/MLS/TRB"]
reso_car_image_dir = ["/mnt/ca6m0/imgs/MLS/CAR","/mnt/ca7m0/imgs/MLS/CAR"]
```

### 1.2 目标需求
- **ca6服务器**: 只下载到ca6路径 (`/mnt/ca6m0/imgs/MLS/*`)
- **ca7服务器**: 只下载到ca7路径 (`/mnt/ca7m0/imgs/MLS/*`)
- **保持兼容性**: 不影响现有的配置结构和其他功能

## 2. 技术方案

### 2.1 主机名检测机制
利用现有的主机名检测功能，在下载器初始化时根据主机名过滤存储路径。

### 2.2 核心实现思路
1. **路径过滤**: 在`GetImageDir`函数中根据主机名过滤返回的存储路径
2. **主机名映射**: 建立主机名到存储路径前缀的映射关系
3. **向后兼容**: 非ca6/ca7主机保持原有行为

## 3. 实现方案

### 3.1 修改文件清单

#### 3.1.1 核心修改文件
- `golevelstore/level_store_helper.go` - 修改GetImageDir函数
- `golevelstore/host_filter.go` - 新增主机名过滤逻辑（新文件）

#### 3.1.2 配置文件
- 生产环境配置文件 - 无需修改，保持现有配置

### 3.2 详细实现步骤

#### 步骤1: 创建主机名过滤模块
创建新文件 `golevelstore/host_filter.go`:

```go
package levelStore

import (
    "os"
    "strings"
    golog "github.com/real-rm/golog"
)

// HostStorageFilter 主机存储路径过滤器
type HostStorageFilter struct {
    hostname string
}

// NewHostStorageFilter 创建主机存储过滤器
func NewHostStorageFilter() *HostStorageFilter {
    hostname, err := os.Hostname()
    if err != nil {
        golog.Warn("Failed to get hostname, using empty hostname", "error", err)
        hostname = ""
    }
    return &HostStorageFilter{hostname: hostname}
}

// FilterStoragePaths 根据主机名过滤存储路径
func (f *HostStorageFilter) FilterStoragePaths(paths []string) []string {
    if f.hostname == "" {
        return paths // 无法获取主机名时返回所有路径
    }

    // 定义主机名到路径前缀的映射
    hostPathMap := map[string]string{
        "ca6": "ca6",
        "ca7": "ca7",
    }

    // 检查是否为需要过滤的主机
    pathPrefix, needFilter := hostPathMap[f.hostname]
    if !needFilter {
        return paths // 非ca6/ca7主机，返回所有路径
    }

    // 过滤路径
    var filteredPaths []string
    for _, path := range paths {
        if strings.Contains(path, pathPrefix) {
            filteredPaths = append(filteredPaths, path)
        }
    }

    if len(filteredPaths) == 0 {
        golog.Warn("No matching storage paths found for host", 
            "hostname", f.hostname, 
            "pathPrefix", pathPrefix,
            "originalPaths", paths)
        return paths // 如果没有匹配的路径，返回原始路径避免服务异常
    }

    golog.Info("Filtered storage paths for host", 
        "hostname", f.hostname,
        "originalCount", len(paths),
        "filteredCount", len(filteredPaths),
        "filteredPaths", filteredPaths)

    return filteredPaths
}
```

#### 步骤2: 修改GetImageDir函数
在 `golevelstore/level_store_helper.go` 中修改GetImageDir函数:

```go
// 在文件顶部添加全局过滤器变量
var globalHostFilter *HostStorageFilter

func init() {
    // 初始化主机过滤器
    globalHostFilter = NewHostStorageFilter()
}

// 修改GetImageDir函数，在返回前应用过滤器
var GetImageDir = func(board string) ([]string, error) {
    // ... 现有的获取路径逻辑保持不变 ...
    
    // 在返回前应用主机过滤器
    if len(dirs) > 0 && globalHostFilter != nil {
        dirs = globalHostFilter.FilterStoragePaths(dirs)
    }

    golog.Info("GetImageDir", "board", board, "dirs", dirs)
    return dirs, nil
}
```

### 3.3 测试验证

#### 3.3.1 单元测试
创建 `golevelstore/host_filter_test.go`:

```go
package levelStore

import (
    "testing"
    "github.com/stretchr/testify/assert"
)

func TestHostStorageFilter_FilterStoragePaths(t *testing.T) {
    tests := []struct {
        name     string
        hostname string
        paths    []string
        expected []string
    }{
        {
            name:     "ca6 host filters ca6 paths",
            hostname: "ca6",
            paths:    []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
            expected: []string{"/mnt/ca6m0/imgs/MLS/TRB"},
        },
        {
            name:     "ca7 host filters ca7 paths", 
            hostname: "ca7",
            paths:    []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
            expected: []string{"/mnt/ca7m0/imgs/MLS/TRB"},
        },
        {
            name:     "other host returns all paths",
            hostname: "other",
            paths:    []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
            expected: []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            filter := &HostStorageFilter{hostname: tt.hostname}
            result := filter.FilterStoragePaths(tt.paths)
            assert.Equal(t, tt.expected, result)
        })
    }
}
```

## 4. 部署方案

### 4.1 部署步骤
1. **代码部署**: 将修改后的代码部署到ca6和ca7服务器
2. **服务重启**: 重启goresodownload服务使新配置生效
3. **验证测试**: 检查日志确认路径过滤正常工作

### 4.2 验证方法
1. **日志检查**: 查看启动日志中的"Filtered storage paths for host"信息
2. **功能测试**: 运行下载任务，确认只写入对应主机的存储路径
3. **监控观察**: 观察磁盘使用情况，确认分离效果

### 4.3 回滚方案
如果出现问题，可以快速回滚到原始代码版本，恢复双路径写入模式。

## 5. 风险评估

### 5.1 低风险
- **向后兼容**: 非ca6/ca7主机行为不变
- **配置不变**: 无需修改现有配置文件
- **渐进部署**: 可以逐台服务器部署验证

### 5.2 注意事项
- **主机名依赖**: 确保ca6/ca7服务器主机名设置正确
- **路径匹配**: 确保存储路径包含正确的ca6/ca7标识
- **日志监控**: 部署后需要监控过滤日志确认正常工作

## 6. 后续优化

### 6.1 可选增强
- **配置化映射**: 将主机名到路径的映射关系配置化
- **更灵活的过滤规则**: 支持正则表达式或更复杂的过滤逻辑
- **监控指标**: 添加存储路径使用的监控指标

### 6.2 长期规划
- **存储策略优化**: 考虑基于负载均衡的智能存储分配
- **容灾机制**: 在单个存储路径不可用时的自动切换机制
