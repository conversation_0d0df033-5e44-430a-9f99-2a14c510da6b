# Backward Compatible Configuration
# This file shows how to use both old and new configuration styles

[contact]
defaultEmail = "<EMAIL>"
defaultEmailFromName = "Your Company"

[dbs]
verbose = 3

[dbs.tmp]
uri = "mongodb://localhost:27017/test"

[golog]
dir = "/tmp/logs"
level = "info"

# ===== OLD STYLE CONFIGURATION (Still Supported) =====
# You can still use the old mailEngine.* format

[mailEngine.gmail]
defaultEmail = "<EMAIL>"

[mailEngine.gmail.auth]
user = "<EMAIL>"
pass = "your-app-password"
host = "smtp.gmail.com"
port = 587

[mailEngine.outlook]
defaultEmail = "<EMAIL>"

[mailEngine.outlook.auth]
user = "<EMAIL>"
pass = "your-password"
host = "smtp-mail.outlook.com"
port = 587

[mailEngine.ses]
defaultEmail = "<EMAIL>"
region = "us-east-1"
accessKeyId = "********************"
secretAccessKey = "Uogp6eMqmbIhxB43mqCYPJYBT9x3vnQZwrawvz6W"

[mailEngine.rmMail]
defaultEmail = "<EMAIL>"
url = "https://ml1.realmaster.cc/send"

# Legacy SMTP configuration (for backward compatibility)
[mailEngine.smtp]
service = "Gmail"

# ===== NEW STYLE CONFIGURATION (Recommended) =====
# If you want to use the new flexible configuration, 
# uncomment the sections below and comment out the old style above

# [emailEngineConfig]
# defaultEngine = "gmail-primary"
# fallbackStrategy = "priority"
# retryCount = 3
# retryDelay = 1000
# enableFailover = true

# [[emailEngine]]
# nameKey = "gmail-primary"
# type = "smtp"
# enabled = true
# defaultEmail = "<EMAIL>"
# priority = 1
# rateLimit = 250
# host = "smtp.gmail.com"
# port = 587
# user = "<EMAIL>"
# password = "your-app-password"
# tls = true
# startTLS = true

# [[emailEngine]]
# nameKey = "outlook-business"
# type = "smtp"
# enabled = true
# defaultEmail = "<EMAIL>"
# priority = 2
# rateLimit = 250
# host = "smtp-mail.outlook.com"
# port = 587
# user = "<EMAIL>"
# password = "your-password"
# tls = true
# startTLS = true

# [[emailEngine]]
# nameKey = "aws-ses-main"
# type = "ses"
# enabled = true
# defaultEmail = "<EMAIL>"
# priority = 1
# rateLimit = 100
# region = "us-east-1"
# accessKeyId = "********************"
# secretAccessKey = "Uogp6eMqmbIhxB43mqCYPJYBT9x3vnQZwrawvz6W"
