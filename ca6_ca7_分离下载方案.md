# CA6/CA7 备份下载方案

## 1. 需求分析

### 1.1 当前架构
- **存储配置**: 每个board类型配置了两个存储路径，分别对应ca6和ca7两个盘
- **下载逻辑**: 当前下载器会同时写入所有配置的存储路径（冗余存储）
- **配置示例**:
```ini
[imageStore]
reso_treb_image_dir = ["/mnt/ca6m0/imgs/MLS/TRB","/mnt/ca7m0/imgs/MLS/TRB"]
reso_car_image_dir = ["/mnt/ca6m0/imgs/MLS/CAR","/mnt/ca7m0/imgs/MLS/CAR"]
```

### 1.2 备份需求
- **ca6服务器**: 运行完整的下载服务，但只下载到ca6路径，形成完整的数据备份
- **ca7服务器**: 运行完整的下载服务，但只下载到ca7路径，形成完整的数据备份
- **目的**: 两套完整的独立数据备份，用于容灾和数据安全

### 1.3 核心问题
1. **队列互斥**: 当前队列使用`FindOneAndUpdate`机制，一个服务器取出任务后，另一个服务器就无法获取同样的任务
2. **数据不一致**: ca6下载了某个property的图片，但ca7可能错过了，导致两边数据不一致
3. **目标**: 需要确保ca6和ca7都能处理相同的任务，下载相同的数据

## 2. 技术方案

### 2.1 方案选择
为了确保两边数据完全一致，有以下方案：

**方案A: 修改队列机制** - 允许多个服务器处理同一个任务
**方案B: 独立队列** - ca6和ca7使用独立的队列
**方案C: 主从复制** - 一个主服务器处理，另一个从服务器复制

**推荐方案A**: 修改队列机制，影响最小，实现简单

### 2.2 核心实现思路
1. **队列共享**: 修改GetNext逻辑，允许多个服务器获取同一个任务
2. **主机标识**: 在队列项中记录已处理的主机列表
3. **存储路径过滤**: 根据主机名只使用对应的存储路径
4. **完成标记**: 只有当所有目标主机都处理完成后，才从队列中移除任务

## 3. 实现方案

### 3.1 修改文件清单

#### 3.1.1 核心修改文件
- `goresodownload/resource_download_queue.go` - 修改队列机制，支持多主机处理
- `golevelstore/level_store_helper.go` - 修改GetImageDir函数，添加主机名过滤
- `golevelstore/host_filter.go` - 新增主机名过滤逻辑（新文件）

#### 3.1.2 数据库变更
- 队列表结构需要添加字段记录已处理的主机列表

#### 3.1.3 配置文件
- 生产环境配置文件 - 无需修改，保持现有配置

### 3.2 详细实现步骤

#### 步骤1: 修改队列数据结构
在 `goresodownload/resource_download_queue.go` 中修改QueueItem结构:

```go
// QueueItem represents a single item in the download queue
type QueueItem struct {
    ID            string    `bson:"_id"`
    Src           string    `bson:"src"`
    Mt            time.Time `bson:"_mt"`
    DlShallEndTs  time.Time `bson:"dlShallEndTs"`
    Priority      int       `bson:"priority"`
    ProcessedHosts []string `bson:"processedHosts,omitempty"` // 新增：已处理的主机列表
}

// 定义目标主机列表
var TargetHosts = []string{"ca6", "ca7"}
```

#### 步骤2: 修改GetNext函数
```go
// GetNext gets the next single resource to download for a specific board
func (q *ResourceDownloadQueue) GetNext(boardType string) (*QueueItem, error) {
    ctx := context.Background()
    hostname, _ := os.Hostname()

    // 如果不是目标主机，使用原有逻辑
    if !isTargetHost(hostname) {
        return q.getNextOriginal(boardType)
    }

    // Filter: items with dlShallEndTs in the past, matching boardType, and not processed by current host
    filter := bson.M{
        "src":          boardType,
        "dlShallEndTs": bson.M{"$lt": time.Now()},
        "processedHosts": bson.M{"$ne": hostname}, // 当前主机未处理过
    }

    // Update: add current host to processedHosts and set new dlShallEndTs
    newDlShallEndTs := time.Now().Add(time.Duration(DOWNLOAD_ALLOWED_MS) * time.Millisecond)
    update := bson.M{
        "$addToSet": bson.M{"processedHosts": hostname}, // 添加当前主机到已处理列表
        "$set": bson.M{"dlShallEndTs": newDlShallEndTs},
    }

    // Options: sort by priority descending
    opts := options.FindOneAndUpdate().
        SetSort(bson.M{"priority": -1}).
        SetReturnDocument(options.After)

    var result QueueItem
    err := q.queueCol.FindOneAndUpdate(ctx, filter, update, opts).Decode(&result)
    if err != nil {
        if err == mongo.ErrNoDocuments {
            return nil, nil // No items available for this host
        }
        return nil, fmt.Errorf("failed to find and update queue item: %w", err)
    }

    golog.Info("Retrieved queue item for host",
        "itemID", result.ID,
        "hostname", hostname,
        "processedHosts", result.ProcessedHosts)

    return &result, nil
}

// isTargetHost 检查是否为目标主机
func isTargetHost(hostname string) bool {
    for _, host := range TargetHosts {
        if hostname == host {
            return true
        }
    }
    return false
}

// getNextOriginal 原有的GetNext逻辑，用于非目标主机
func (q *ResourceDownloadQueue) getNextOriginal(boardType string) (*QueueItem, error) {
    // ... 原有的GetNext实现 ...
}
```

#### 步骤3: 添加队列清理机制
```go
// MarkCompleted 标记任务完成，如果所有目标主机都处理完成则删除任务
func (q *ResourceDownloadQueue) MarkCompleted(itemID string) error {
    ctx := context.Background()

    // 查询当前任务状态
    var item QueueItem
    err := q.queueCol.FindOne(ctx, bson.M{"_id": itemID}).Decode(&item)
    if err != nil {
        return fmt.Errorf("failed to find queue item: %w", err)
    }

    // 检查是否所有目标主机都已处理
    allProcessed := true
    for _, targetHost := range TargetHosts {
        found := false
        for _, processedHost := range item.ProcessedHosts {
            if processedHost == targetHost {
                found = true
                break
            }
        }
        if !found {
            allProcessed = false
            break
        }
    }

    if allProcessed {
        // 所有目标主机都已处理，删除任务
        _, err := q.queueCol.DeleteOne(ctx, bson.M{"_id": itemID})
        if err != nil {
            return fmt.Errorf("failed to delete completed queue item: %w", err)
        }
        golog.Info("Queue item completed and removed", "itemID", itemID)
    } else {
        golog.Debug("Queue item partially completed",
            "itemID", itemID,
            "processedHosts", item.ProcessedHosts,
            "targetHosts", TargetHosts)
    }

    return nil
}
```

#### 步骤4: 创建主机存储过滤模块
创建新文件 `golevelstore/host_filter.go`:

```go
package levelStore

import (
    "os"
    "strings"
    golog "github.com/real-rm/golog"
)

// HostStorageFilter 主机存储路径过滤器
type HostStorageFilter struct {
    hostname string
}

// NewHostStorageFilter 创建主机存储过滤器
func NewHostStorageFilter() *HostStorageFilter {
    hostname, err := os.Hostname()
    if err != nil {
        golog.Warn("Failed to get hostname, using empty hostname", "error", err)
        hostname = ""
    }
    return &HostStorageFilter{hostname: hostname}
}

// FilterStoragePaths 根据主机名过滤存储路径
func (f *HostStorageFilter) FilterStoragePaths(paths []string) []string {
    if f.hostname == "" {
        return paths // 无法获取主机名时返回所有路径
    }

    // 定义主机名到路径前缀的映射
    hostPathMap := map[string]string{
        "ca6": "ca6",
        "ca7": "ca7",
    }

    // 检查是否为需要过滤的主机
    pathPrefix, needFilter := hostPathMap[f.hostname]
    if !needFilter {
        return paths // 非ca6/ca7主机，返回所有路径
    }

    // 过滤路径
    var filteredPaths []string
    for _, path := range paths {
        if strings.Contains(path, pathPrefix) {
            filteredPaths = append(filteredPaths, path)
        }
    }

    if len(filteredPaths) == 0 {
        golog.Warn("No matching storage paths found for host",
            "hostname", f.hostname,
            "pathPrefix", pathPrefix,
            "originalPaths", paths)
        return paths // 如果没有匹配的路径，返回原始路径避免服务异常
    }

    golog.Info("Filtered storage paths for host",
        "hostname", f.hostname,
        "originalCount", len(paths),
        "filteredCount", len(filteredPaths),
        "filteredPaths", filteredPaths)

    return filteredPaths
}
```

#### 步骤5: 修改GetImageDir函数
在 `golevelstore/level_store_helper.go` 中修改GetImageDir函数:

```go
// 在文件顶部添加全局过滤器变量
var globalHostFilter *HostStorageFilter

func init() {
    // 初始化主机过滤器
    globalHostFilter = NewHostStorageFilter()
}

// 修改GetImageDir函数，在返回前应用过滤器
var GetImageDir = func(board string) ([]string, error) {
    // ... 现有的获取路径逻辑保持不变 ...

    // 在返回前应用主机过滤器
    if len(dirs) > 0 && globalHostFilter != nil {
        dirs = globalHostFilter.FilterStoragePaths(dirs)
    }

    golog.Info("GetImageDir", "board", board, "dirs", dirs)
    return dirs, nil
}
```

#### 步骤6: 修改处理完成逻辑
在 `goresodownload/cmd/goresodownload/processingQueue.go` 中修改任务完成处理:

```go
// 在processQueueItem函数的最后添加完成标记
func processQueueItem(queueItem *goresodownload.QueueItem) error {
    // ... 现有的处理逻辑 ...

    // 处理完成后，标记任务完成
    if err := downloadQueue.MarkCompleted(queueItem.ID); err != nil {
        golog.Warn("Failed to mark queue item as completed", "itemID", queueItem.ID, "error", err)
        // 不返回错误，避免影响主要处理流程
    }

    return nil
}
```

### 3.3 数据库迁移

#### 3.3.1 添加processedHosts字段
需要为现有的队列表添加新字段：

```javascript
// MongoDB 迁移脚本
db.resource_download_queue.updateMany(
    { processedHosts: { $exists: false } },
    { $set: { processedHosts: [] } }
);

// 创建索引以提高查询性能
db.resource_download_queue.createIndex({
    "src": 1,
    "dlShallEndTs": 1,
    "processedHosts": 1
});
```

### 3.4 测试验证

#### 3.4.1 单元测试
创建 `goresodownload/resource_download_queue_test.go`:

```go
func TestGetNext_MultiHost(t *testing.T) {
    // 测试多主机队列处理
    // 1. ca6获取任务后，ca7仍能获取同一任务
    // 2. 两个主机都处理完成后，任务被删除
}
```

创建 `golevelstore/host_filter_test.go`:

```go
package levelStore

import (
    "testing"
    "github.com/stretchr/testify/assert"
)

func TestHostStorageFilter_FilterStoragePaths(t *testing.T) {
    tests := []struct {
        name     string
        hostname string
        paths    []string
        expected []string
    }{
        {
            name:     "ca6 host filters ca6 paths",
            hostname: "ca6",
            paths:    []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
            expected: []string{"/mnt/ca6m0/imgs/MLS/TRB"},
        },
        {
            name:     "ca7 host filters ca7 paths",
            hostname: "ca7",
            paths:    []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
            expected: []string{"/mnt/ca7m0/imgs/MLS/TRB"},
        },
        {
            name:     "other host returns all paths",
            hostname: "other",
            paths:    []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
            expected: []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            filter := &HostStorageFilter{hostname: tt.hostname}
            result := filter.FilterStoragePaths(tt.paths)
            assert.Equal(t, tt.expected, result)
        })
    }
}
```

## 4. 部署方案

### 4.1 部署步骤
1. **代码部署**: 将修改后的代码部署到ca6和ca7服务器
2. **服务重启**: 重启goresodownload服务使新配置生效
3. **验证测试**: 检查日志确认路径过滤正常工作

### 4.2 验证方法
1. **日志检查**: 查看启动日志中的"Filtered storage paths for host"信息
2. **功能测试**: 运行下载任务，确认只写入对应主机的存储路径
3. **监控观察**: 观察磁盘使用情况，确认分离效果

### 4.3 回滚方案
如果出现问题，可以快速回滚到原始代码版本，恢复双路径写入模式。

## 5. 风险评估

### 5.1 低风险
- **向后兼容**: 非ca6/ca7主机行为不变
- **配置不变**: 无需修改现有配置文件
- **渐进部署**: 可以逐台服务器部署验证

### 5.2 注意事项
- **主机名依赖**: 确保ca6/ca7服务器主机名设置正确
- **路径匹配**: 确保存储路径包含正确的ca6/ca7标识
- **日志监控**: 部署后需要监控过滤日志确认正常工作

## 6. 后续优化

### 6.1 可选增强
- **配置化映射**: 将主机名到路径的映射关系配置化
- **更灵活的过滤规则**: 支持正则表达式或更复杂的过滤逻辑
- **监控指标**: 添加存储路径使用的监控指标

### 6.2 长期规划
- **存储策略优化**: 考虑基于负载均衡的智能存储分配
- **容灾机制**: 在单个存储路径不可用时的自动切换机制
