package main

import (
	"fmt"
	"log"

	gomail "github.com/real-rm/gomail"
)

func main() {
	fmt.Println("=== Backward Compatible Email Configuration Example ===\n")

	// Method 1: Try new flexible configuration first
	err := tryNewFlexibleConfig()
	if err != nil {
		log.Printf("New flexible config failed: %v", err)
		
		// Method 2: Fall back to old configuration
		err = tryOldConfig()
		if err != nil {
			log.Fatalf("Both new and old config failed: %v", err)
		}
	}
}

// tryNewFlexibleConfig attempts to use the new flexible configuration
func tryNewFlexibleConfig() error {
	fmt.Println("Trying new flexible configuration...")
	
	// Load flexible configuration
	mailer, err := gomail.LoadFlexibleConfig()
	if err != nil {
		return fmt.Errorf("failed to load flexible config: %v", err)
	}

	fmt.Println("✓ New flexible configuration loaded successfully!")
	
	// Show available engines
	engines := mailer.GetAvailableEngines()
	fmt.Printf("Available engines: %v\n", engines)

	// Send email using new method
	msg := &gomail.EmailMessage{
		To:      []string{"<EMAIL>"},
		Subject: "Test Email from New Config",
		Text:    "This email was sent using the new flexible configuration.",
		HTML:    "<p>This email was sent using the <strong>new flexible configuration</strong>.</p>",
	}

	// Try to send with the first available engine
	if len(engines) > 0 {
		err = mailer.SendMail(engines[0], msg)
		if err != nil {
			return fmt.Errorf("failed to send email: %v", err)
		}
		fmt.Printf("✓ Email sent successfully using engine: %s\n", engines[0])
	}

	// Try fallback mechanism
	err = mailer.SendMailWithFallback(msg)
	if err != nil {
		return fmt.Errorf("failed to send email with fallback: %v", err)
	}
	fmt.Println("✓ Email sent successfully using fallback mechanism!")

	return nil
}

// tryOldConfig attempts to use the old configuration method
func tryOldConfig() error {
	fmt.Println("\nFalling back to old configuration...")
	
	// Load old configuration
	mailer, err := gomail.GetSendMailObj()
	if err != nil {
		return fmt.Errorf("failed to load old config: %v", err)
	}

	fmt.Println("✓ Old configuration loaded successfully!")

	// Send email using old method
	msg := &gomail.EmailMessage{
		To:      []string{"<EMAIL>"},
		Subject: "Test Email from Old Config",
		Text:    "This email was sent using the old configuration method.",
		HTML:    "<p>This email was sent using the <strong>old configuration method</strong>.</p>",
	}

	// Try different engines in order of preference
	engines := []string{"gmail", "outlook", "ses", "rmMail"}
	
	for _, engineName := range engines {
		err = mailer.SendMail(engineName, msg)
		if err == nil {
			fmt.Printf("✓ Email sent successfully using old engine: %s\n", engineName)
			return nil
		}
		fmt.Printf("✗ Engine %s failed: %v\n", engineName, err)
	}

	return fmt.Errorf("all old engines failed")
}

// demonstrateConfigMigration shows how to migrate from old to new config
func demonstrateConfigMigration() {
	fmt.Println("\n=== Configuration Migration Guide ===")
	
	fmt.Println(`
Old Configuration Style:
[mailEngine.gmail]
defaultEmail = "<EMAIL>"
[mailEngine.gmail.auth]
user = "<EMAIL>"
pass = "password"
host = "smtp.gmail.com"
port = 587

Usage: mailer.SendMail("gmail", msg)

New Configuration Style:
[[emailEngine]]
nameKey = "gmail-primary"
type = "smtp"
enabled = true
defaultEmail = "<EMAIL>"
priority = 1
host = "smtp.gmail.com"
port = 587
user = "<EMAIL>"
password = "password"

Usage: mailer.SendMail("gmail-primary", msg)

Benefits of New Style:
1. Multiple instances of same type (e.g., gmail-primary, gmail-backup)
2. Priority-based fallback
3. More flexible configuration per engine
4. Better error handling and retry logic
5. Unified configuration format for all engine types
`)
}

// showCompatibilityMatrix shows which methods work with which configs
func showCompatibilityMatrix() {
	fmt.Println("\n=== Compatibility Matrix ===")
	fmt.Println(`
Configuration Type    | LoadFlexibleConfig() | GetSendMailObj()
---------------------|---------------------|------------------
New [[emailEngine]]  | ✓ Yes               | ✗ No
Old [mailEngine.*]   | ✓ Yes (auto-detect) | ✓ Yes
Mixed (both)         | ✓ Yes (prefers new) | ✓ Yes (uses old)

Recommendation:
- For new projects: Use LoadFlexibleConfig() with [[emailEngine]]
- For existing projects: Keep using GetSendMailObj() or migrate gradually
- For migration: LoadFlexibleConfig() can auto-detect old config
`)
}

// Example of gradual migration
func gradualMigrationExample() {
	fmt.Println("\n=== Gradual Migration Example ===")
	
	// Step 1: Try new config, fall back to old
	mailer, err := gomail.LoadFlexibleConfig()
	if err != nil {
		// Fall back to old method
		oldMailer, oldErr := gomail.GetSendMailObj()
		if oldErr != nil {
			log.Fatalf("Both configs failed: new=%v, old=%v", err, oldErr)
		}
		
		// Use old mailer
		msg := &gomail.EmailMessage{
			To:      []string{"<EMAIL>"},
			Subject: "Test",
			Text:    "Test message",
		}
		
		oldMailer.SendMail("gmail", msg)
		return
	}
	
	// Use new mailer
	msg := &gomail.EmailMessage{
		To:      []string{"<EMAIL>"},
		Subject: "Test",
		Text:    "Test message",
	}
	
	mailer.SendMailWithFallback(msg)
}
