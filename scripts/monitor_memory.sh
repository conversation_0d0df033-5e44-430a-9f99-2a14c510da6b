#!/bin/bash

# Memory monitoring script for goresodownload service
# Usage: ./monitor_memory.sh [service_name]

SERVICE_NAME=${1:-"<EMAIL>"}
LOG_FILE="/tmp/memory_monitor_$(date +%Y%m%d_%H%M%S).log"

echo "Starting memory monitoring for service: $SERVICE_NAME"
echo "Log file: $LOG_FILE"
echo "Press Ctrl+C to stop monitoring"
echo ""

# Function to get memory usage
get_memory_usage() {
    local memory_info=$(systemctl --user status "$SERVICE_NAME" 2>/dev/null | grep "Memory:" | awk '{print $2}')
    echo "$memory_info"
}

# Function to get process info
get_process_info() {
    local pid=$(systemctl --user show "$SERVICE_NAME" --property=MainPID --value 2>/dev/null)
    if [ "$pid" != "0" ] && [ -n "$pid" ]; then
        ps -p "$pid" -o pid,ppid,rss,vsz,pcpu,pmem,cmd --no-headers 2>/dev/null
    else
        echo "Service not running or PID not found"
    fi
}

# Function to log memory stats
log_memory_stats() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local memory=$(get_memory_usage)
    local process_info=$(get_process_info)
    
    echo "[$timestamp] Memory: $memory" | tee -a "$LOG_FILE"
    if [ "$process_info" != "Service not running or PID not found" ]; then
        echo "[$timestamp] Process: $process_info" | tee -a "$LOG_FILE"
    else
        echo "[$timestamp] $process_info" | tee -a "$LOG_FILE"
    fi
    echo "" | tee -a "$LOG_FILE"
}

# Function to check for memory leaks
check_memory_leak() {
    local current_memory_mb=$(get_memory_usage | sed 's/[^0-9.]//g')
    
    if [ -n "$current_memory_mb" ] && [ "$current_memory_mb" != "" ]; then
        # Convert to MB if in GB
        if echo "$current_memory_mb" | grep -q "G"; then
            current_memory_mb=$(echo "$current_memory_mb" | sed 's/G//' | awk '{print $1 * 1024}')
        fi
        
        # Alert if memory usage is high
        if (( $(echo "$current_memory_mb > 2048" | bc -l) )); then
            echo "⚠️  HIGH MEMORY USAGE DETECTED: ${current_memory_mb}MB" | tee -a "$LOG_FILE"
        fi
        
        if (( $(echo "$current_memory_mb > 4096" | bc -l) )); then
            echo "🚨 CRITICAL MEMORY USAGE: ${current_memory_mb}MB - Consider restarting service!" | tee -a "$LOG_FILE"
        fi
    fi
}

# Trap Ctrl+C
trap 'echo "Monitoring stopped. Log saved to: $LOG_FILE"; exit 0' INT

# Main monitoring loop
while true; do
    log_memory_stats
    check_memory_leak
    sleep 30  # Check every 30 seconds
done
