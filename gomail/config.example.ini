# Example configuration for multiple SMTP services
# Copy this file and modify according to your needs

[contact]
defaultEmail = "<EMAIL>"
defaultEmailFromName = "Your Company"
defaultRTEmail = "<EMAIL>"
defaultReciveEmail = "<EMAIL>"
devEmails = "<EMAIL>"

[dbs]
verbose = 3

[dbs.tmp]
uri = "mongodb://localhost:27017/test"

[golog]
dir = "/tmp/logs"
level = "info"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"

[preDefColls.emailMXFailed]
collName = "email_mx_failed"
dbName = "tmp"

[preDefColls.emailWhitelist]
collName = "email_whitelist"
dbName = "tmp"

[preDefColls.mailLog]
collName = "mail_log"
dbName = "tmp"

  [preDefColls.mailLog.options]
  expireAfterSeconds = 2_592_000

    [preDefColls.mailLog.options.timeseries]
    metaField = "metadata"
    timeField = "timestamp"

[mailEngine]
mailEngine = "gmail"  # Default engine

  # Gmail SMTP Configuration
  [mailEngine.gmail]
  defaultEmail = "<EMAIL>"

    [mailEngine.gmail.auth]
    user = "<EMAIL>"
    pass = "your-app-password"  # Use App Password for Gmail
    host = "smtp.gmail.com"
    port = 587

  # Outlook/Hotmail SMTP Configuration
  [mailEngine.outlook]
  defaultEmail = "<EMAIL>"

    [mailEngine.outlook.auth]
    user = "<EMAIL>"
    pass = "your-password"
    host = "smtp-mail.outlook.com"
    port = 587

  # QQ Mail SMTP Configuration
  [mailEngine.qq]
  defaultEmail = "<EMAIL>"

    [mailEngine.qq.auth]
    user = "<EMAIL>"
    pass = "your-authorization-code"  # Use authorization code for QQ
    host = "smtp.qq.com"
    port = 587

  # Custom SMTP Configuration
  [mailEngine.custom]
  defaultEmail = "<EMAIL>"

    [mailEngine.custom.auth]
    user = "<EMAIL>"
    pass = "your-password"
    host = "smtp.yourcompany.com"
    port = 587

  # Mock Mail for Testing
  [mailEngine.mockMail]
  mock = false
  verbose = 3

  # Legacy SMTP configuration (for backward compatibility)
  [mailEngine.smtp]
  service = "Gmail"  # This will use the gmail configuration above

# Multiple engine list for load balancing (optional)
[[mailEngineList]]
email = "<EMAIL>"
engine = "gmail"

[[mailEngineList]]
email = "<EMAIL>"
engine = "outlook"
