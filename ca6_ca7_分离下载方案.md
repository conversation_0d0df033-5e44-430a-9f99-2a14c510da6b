# CA6/CA7 备份下载方案

## 1. 需求分析

### 1.1 当前架构
- **存储配置**: 每个board类型配置了两个存储路径，分别对应ca6和ca7两个盘
- **下载逻辑**: 当前下载器会同时写入所有配置的存储路径（冗余存储）
- **配置示例**:
```ini
[imageStore]
reso_treb_image_dir = ["/mnt/ca6m0/imgs/MLS/TRB","/mnt/ca7m0/imgs/MLS/TRB"]
reso_car_image_dir = ["/mnt/ca6m0/imgs/MLS/CAR","/mnt/ca7m0/imgs/MLS/CAR"]
```

### 1.2 备份需求
- **ca6服务器**: 运行完整的下载服务，但只下载到ca6路径，形成完整的数据备份
- **ca7服务器**: 运行完整的下载服务，但只下载到ca7路径，形成完整的数据备份
- **目的**: 两套完整的独立数据备份，用于容灾和数据安全

### 1.3 核心问题
1. **队列互斥**: 当前队列使用`FindOneAndUpdate`机制，一个服务器取出任务后，另一个服务器就无法获取同样的任务
2. **数据不一致**: ca6下载了某个property的图片，但ca7可能错过了，导致两边数据不一致
3. **路径竞态**: property变化时，ca6和ca7可能计算出不同的phoP路径，导致存储到不同目录
4. **目标**: 需要确保ca6和ca7都能处理相同的任务，下载到相同的路径

## 2. 技术方案

### 2.1 方案选择
为了确保两边数据完全一致，有以下方案：

**方案A: 修改队列机制** - 允许多个服务器处理同一个任务
**方案B: 独立队列** - ca6和ca7使用独立的队列
**方案C: 主从复制** - 一个主服务器处理，另一个从服务器复制

**推荐方案B**: 独立队列，逻辑清晰，易于管理

### 2.2 核心实现思路
1. **独立队列**: ca6和ca7使用不同的队列集合
2. **队列同步**: 添加任务时同时添加到两个队列，并**预先计算phoP路径**
3. **路径锁定**: 在队列中保存计算好的phoP路径，避免重复计算
4. **原子更新**: 使用数据库原子操作确保路径一致性
5. **完成状态跟踪**: 在merged表中记录哪些主机已完成下载

## 3. 实现方案

### 3.1 修改文件清单

#### 3.1.1 核心修改文件
- `goresodownload/resource_download_queue.go` - 修改队列机制，支持独立队列
- `goresodownload/cmd/batch/addToQueue/main.go` - 修改添加队列逻辑，同时添加到两个队列
- `goresodownload/downloader.go` - 修改merged表更新逻辑，添加完成状态跟踪
- `golevelstore/level_store_helper.go` - 修改GetImageDir函数，添加主机名过滤
- `golevelstore/host_filter.go` - 新增主机名过滤逻辑（新文件）

#### 3.1.2 数据库变更
- 创建独立的队列集合：`resource_download_queue_ca6`, `resource_download_queue_ca7`
- 队列表添加新字段：`phoP`, `propTs`, `listingKey`, `board`
- merged表添加下载完成状态字段：`downloadStatus`

#### 3.1.3 配置文件
- 生产环境配置文件 - 无需修改，保持现有配置

### 3.2 详细实现步骤

#### 步骤1: 修改队列数据结构
在 `goresodownload/resource_download_queue.go` 中修改QueueItem结构:

```go
// QueueItem represents a single item in the download queue
type QueueItem struct {
    ID            string    `bson:"_id"`
    Src           string    `bson:"src"`
    Mt            time.Time `bson:"_mt"`
    DlShallEndTs  time.Time `bson:"dlShallEndTs"`
    Priority      int       `bson:"priority"`
    PhoP          string    `bson:"phoP"`          // 新增：预计算的phoP路径
    PropTs        time.Time `bson:"propTs"`        // 新增：用于路径计算的时间戳
    ListingKey    string    `bson:"listingKey"`    // 新增：listing key
    Board         string    `bson:"board"`         // 新增：board类型
}

// ResourceDownloadQueue manages the download queue operations
type ResourceDownloadQueue struct {
    queueCol *gomongo.MongoCollection
    hostname string // 新增：当前主机名
}

// NewResourceDownloadQueue creates a new ResourceDownloadQueue instance
func NewResourceDownloadQueue(queueCol *gomongo.MongoCollection) *ResourceDownloadQueue {
    hostname, err := os.Hostname()
    if err != nil {
        golog.Warn("Failed to get hostname", "error", err)
        hostname = ""
    }

    return &ResourceDownloadQueue{
        queueCol: queueCol,
        hostname: hostname,
    }
}
```

#### 步骤2: 修改AddToQueue函数，预先计算phoP路径
```go
// AddToQueueWithPath adds a resource to the download queue with pre-calculated path
func (q *ResourceDownloadQueue) AddToQueueWithPath(id string, priority int, src string, propDoc bson.M) error {
    // Parse default download start time
    defaultStartTime, err := time.Parse(time.RFC3339, DEFAULT_DOWNLOAD_START_TS)
    if err != nil {
        return fmt.Errorf("failed to parse default start time: %w", err)
    }

    // 预先计算phoP路径和相关信息
    propTs, err := goresodownload.GetPropTsForPath(propDoc, src)
    if err != nil {
        return fmt.Errorf("failed to get PropTs for path: %w", err)
    }

    // 获取listing key
    var listingKey string
    if src == "TRB" {
        listingKey, _ = propDoc["ListingKey"].(string)
    } else {
        listingKey, _ = propDoc["ListingId"].(string)
    }

    // 计算phoP路径
    phoP, err := levelStore.GetFullFilePathForProp(propTs, src, listingKey)
    if err != nil {
        return fmt.Errorf("failed to get full file path: %w", err)
    }

    // Create update document with pre-calculated path
    ctx := context.Background()
    filter := bson.M{"_id": id}
    updateDoc := bson.M{
        "_id":          id,
        "src":          src,
        "_mt":          time.Now(),
        "dlShallEndTs": defaultStartTime,
        "priority":     priority,
        "phoP":         phoP,         // 预计算的路径
        "propTs":       propTs,       // 时间戳
        "listingKey":   listingKey,   // listing key
        "board":        src,          // board类型
    }

    // 如果是ca6或ca7主机，需要同时添加到两个队列
    if q.hostname == "ca6" || q.hostname == "ca7" {
        // 添加到ca6队列
        ca6Col := gomongo.Coll("rni", "resource_download_queue_ca6")
        _, err := ca6Col.ReplaceOne(ctx, filter, updateDoc, options.Replace().SetUpsert(true))
        if err != nil {
            return fmt.Errorf("failed to upsert to ca6 queue: %w", err)
        }

        // 添加到ca7队列
        ca7Col := gomongo.Coll("rni", "resource_download_queue_ca7")
        _, err = ca7Col.ReplaceOne(ctx, filter, updateDoc, options.Replace().SetUpsert(true))
        if err != nil {
            return fmt.Errorf("failed to upsert to ca7 queue: %w", err)
        }

        golog.Info("Added to both ca6 and ca7 queues with pre-calculated path",
            "id", id,
            "priority", priority,
            "phoP", phoP,
            "propTs", propTs)
    } else {
        // 非ca6/ca7主机，使用默认队列
        _, err := q.queueCol.ReplaceOne(ctx, filter, updateDoc, options.Replace().SetUpsert(true))
        if err != nil {
            return fmt.Errorf("failed to upsert to default queue: %w", err)
        }
    }

    return nil
}

// 保持原有的AddToQueue函数用于向后兼容
func (q *ResourceDownloadQueue) AddToQueue(id string, priority int, src string) error {
    // 对于没有propDoc的情况，从数据库获取
    collectionName, exists := goresodownload.BoardMergedTable[src]
    if !exists {
        return fmt.Errorf("unknown board type: %s", src)
    }

    coll := gomongo.Coll("rni", collectionName)
    var propDoc bson.M
    err := coll.FindOne(context.Background(), bson.M{"_id": id}).Decode(&propDoc)
    if err != nil {
        return fmt.Errorf("failed to get property document: %w", err)
    }

    return q.AddToQueueWithPath(id, priority, src, propDoc)
}
```

#### 步骤3: 修改GetNext函数使用独立队列
```go
// GetNext gets the next single resource to download for a specific board
func (q *ResourceDownloadQueue) GetNext(boardType string) (*QueueItem, error) {
    ctx := context.Background()

    // 根据主机名使用对应的队列集合
    var targetCol *gomongo.MongoCollection
    switch q.hostname {
    case "ca6":
        targetCol = gomongo.Coll("rni", "resource_download_queue_ca6")
    case "ca7":
        targetCol = gomongo.Coll("rni", "resource_download_queue_ca7")
    default:
        targetCol = q.queueCol // 使用默认队列
    }

    // Filter: items with dlShallEndTs in the past and matching boardType
    filter := bson.M{
        "src":          boardType,
        "dlShallEndTs": bson.M{"$lt": time.Now()},
    }

    // Update: set dlShallEndTs to current time + DOWNLOAD_ALLOWED_MS
    newDlShallEndTs := time.Now().Add(time.Duration(DOWNLOAD_ALLOWED_MS) * time.Millisecond)
    update := bson.M{
        "$set": bson.M{
            "dlShallEndTs": newDlShallEndTs,
        },
    }

    // Options: sort by priority descending (highest priority first)
    opts := options.FindOneAndUpdate().
        SetSort(bson.M{"priority": -1}).
        SetReturnDocument(options.After)

    var result QueueItem
    err := targetCol.FindOneAndUpdate(ctx, filter, update, opts).Decode(&result)
    if err != nil {
        if err == mongo.ErrNoDocuments {
            return nil, nil // No items available
        }
        return nil, fmt.Errorf("failed to find and update queue item: %w", err)
    }

    golog.Info("Retrieved queue item",
        "itemID", result.ID,
        "hostname", q.hostname,
        "queueCollection", targetCol.Name())

    return &result, nil
}
```

#### 步骤4: 修改下载器处理逻辑，使用队列中的phoP路径
在 `goresodownload/downloader.go` 中修改updateMergedDoc函数:

```go
// updateMergedDocWithPath updates the merged document using the path from queue item
func (d *Downloader) updateMergedDocWithPath(result *AnalysisResult, thumbnailHash int32, board string, queuePhoP string) error {
    if result == nil {
        return fmt.Errorf("result is nil")
    }

    if d.MergedCol == nil {
        golog.Warn("MergedCol is nil, skipping document update", "ID", result.ID)
        return nil
    }

    ctx := context.Background()
    hostname, _ := os.Hostname()

    // 使用原子操作确保phoP路径的一致性
    filter := bson.M{"_id": result.ID}

    // 准备更新文档
    update := bson.M{"$set": bson.M{}}
    unsetFields := bson.M{}

    // 使用队列中预计算的phoP路径
    if queuePhoP != "" {
        // 使用原子操作：只有当phoP不存在或为空时才设置
        update["$setOnInsert"] = bson.M{"phoP": queuePhoP}

        // 如果phoP已存在但不同，记录警告但继续使用现有路径
        var existingDoc bson.M
        err := d.MergedCol.FindOne(ctx, bson.M{"_id": result.ID}).Decode(&existingDoc)
        if err == nil {
            if existingPhoP, exists := existingDoc["phoP"]; exists && existingPhoP != nil {
                if existingPhoPStr, ok := existingPhoP.(string); ok && existingPhoPStr != "" {
                    if existingPhoPStr != queuePhoP {
                        golog.Warn("phoP path mismatch detected",
                            "ID", result.ID,
                            "hostname", hostname,
                            "existingPhoP", existingPhoPStr,
                            "queuePhoP", queuePhoP)
                        // 使用已存在的路径，不更新phoP
                    }
                } else {
                    // phoP为空，使用队列中的路径
                    update["$set"].(bson.M)["phoP"] = queuePhoP
                }
            } else {
                // phoP不存在，使用队列中的路径
                update["$set"].(bson.M)["phoP"] = queuePhoP
            }
        }
    }

    // Handle PhoLH
    if len(result.PhoLH) > 0 {
        update["$set"].(bson.M)["phoLH"] = result.PhoLH
    } else {
        unsetFields["phoLH"] = ""
    }

    // Handle DocLH
    if len(result.DocLH) > 0 {
        update["$set"].(bson.M)["docLH"] = result.DocLH
    } else {
        unsetFields["docLH"] = ""
    }

    // Add thumbnail hash if available
    if thumbnailHash != 0 {
        update["$set"].(bson.M)["tnLH"] = thumbnailHash
    } else {
        unsetFields["tnLH"] = ""
    }

    // 更新下载状态
    if hostname == "ca6" || hostname == "ca7" {
        // 使用原子操作更新下载状态
        statusUpdate := bson.M{
            "$set": bson.M{
                fmt.Sprintf("downloadStatus.%s", hostname): true,
            },
        }

        // 先更新下载状态
        _, err := d.MergedCol.UpdateOne(ctx, filter, statusUpdate, options.Update().SetUpsert(true))
        if err != nil {
            golog.Warn("Failed to update download status", "error", err, "ID", result.ID)
        }

        golog.Info("Updated download status",
            "ID", result.ID,
            "hostname", hostname)
    }

    // Only add $unset to update if there are fields to unset
    if len(unsetFields) > 0 {
        update["$unset"] = unsetFields
    }

    // Check if there's anything to update
    setFields := update["$set"].(bson.M)
    if len(setFields) == 0 && len(unsetFields) == 0 {
        golog.Debug("updateMergedDoc", "no fields to update")
        return nil
    }

    // 执行主要的更新操作
    updateResult, err := d.MergedCol.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
    if err != nil {
        golog.Error("Failed to update merged document", "error", err, "ID", result.ID)
        return err
    }

    golog.Info("Successfully updated merged document",
        "ID", result.ID,
        "hostname", hostname,
        "queuePhoP", queuePhoP,
        "modifiedCount", updateResult.ModifiedCount)

    return nil
}

// 修改原有的updateMergedDoc函数以支持队列路径
func (d *Downloader) updateMergedDoc(result *AnalysisResult, thumbnailHash int32, board string) error {
    return d.updateMergedDocWithPath(result, thumbnailHash, board, "")
}
```

#### 步骤5: 创建主机存储过滤模块
创建新文件 `golevelstore/host_filter.go`:

```go
package levelStore

import (
    "os"
    "strings"
    golog "github.com/real-rm/golog"
)

// HostStorageFilter 主机存储路径过滤器
type HostStorageFilter struct {
    hostname string
}

// NewHostStorageFilter 创建主机存储过滤器
func NewHostStorageFilter() *HostStorageFilter {
    hostname, err := os.Hostname()
    if err != nil {
        golog.Warn("Failed to get hostname, using empty hostname", "error", err)
        hostname = ""
    }
    return &HostStorageFilter{hostname: hostname}
}

// FilterStoragePaths 根据主机名过滤存储路径
func (f *HostStorageFilter) FilterStoragePaths(paths []string) []string {
    if f.hostname == "" {
        return paths // 无法获取主机名时返回所有路径
    }

    // 定义主机名到路径前缀的映射
    hostPathMap := map[string]string{
        "ca6": "ca6",
        "ca7": "ca7",
    }

    // 检查是否为需要过滤的主机
    pathPrefix, needFilter := hostPathMap[f.hostname]
    if !needFilter {
        return paths // 非ca6/ca7主机，返回所有路径
    }

    // 过滤路径
    var filteredPaths []string
    for _, path := range paths {
        if strings.Contains(path, pathPrefix) {
            filteredPaths = append(filteredPaths, path)
        }
    }

    if len(filteredPaths) == 0 {
        golog.Warn("No matching storage paths found for host",
            "hostname", f.hostname,
            "pathPrefix", pathPrefix,
            "originalPaths", paths)
        return paths // 如果没有匹配的路径，返回原始路径避免服务异常
    }

    golog.Info("Filtered storage paths for host",
        "hostname", f.hostname,
        "originalCount", len(paths),
        "filteredCount", len(filteredPaths),
        "filteredPaths", filteredPaths)

    return filteredPaths
}
```

#### 步骤6: 修改GetImageDir函数
在 `golevelstore/level_store_helper.go` 中修改GetImageDir函数:

```go
// 在文件顶部添加全局过滤器变量
var globalHostFilter *HostStorageFilter

func init() {
    // 初始化主机过滤器
    globalHostFilter = NewHostStorageFilter()
}

// 修改GetImageDir函数，在返回前应用过滤器
var GetImageDir = func(board string) ([]string, error) {
    // ... 现有的获取路径逻辑保持不变 ...

    // 在返回前应用主机过滤器
    if len(dirs) > 0 && globalHostFilter != nil {
        dirs = globalHostFilter.FilterStoragePaths(dirs)
    }

    golog.Info("GetImageDir", "board", board, "dirs", dirs)
    return dirs, nil
}
```

### 3.3 数据库迁移

#### 3.3.1 创建独立队列集合
```javascript
// MongoDB 迁移脚本

// 1. 创建ca6队列集合
db.createCollection("resource_download_queue_ca6");

// 2. 创建ca7队列集合
db.createCollection("resource_download_queue_ca7");

// 3. 复制现有队列数据到新集合
db.resource_download_queue.find().forEach(function(doc) {
    db.resource_download_queue_ca6.insert(doc);
    db.resource_download_queue_ca7.insert(doc);
});

// 4. 创建索引以提高查询性能
db.resource_download_queue_ca6.createIndex({
    "src": 1,
    "dlShallEndTs": 1,
    "priority": -1
});

db.resource_download_queue_ca7.createIndex({
    "src": 1,
    "dlShallEndTs": 1,
    "priority": -1
});
```

#### 3.3.2 为队列表添加新字段
```javascript
// 为新队列表添加必要字段的索引
db.resource_download_queue_ca6.createIndex({
    "src": 1,
    "dlShallEndTs": 1,
    "priority": -1,
    "phoP": 1
});

db.resource_download_queue_ca7.createIndex({
    "src": 1,
    "dlShallEndTs": 1,
    "priority": -1,
    "phoP": 1
});
```

#### 3.3.3 为merged表添加下载状态字段
```javascript
// 为所有merged表添加downloadStatus字段
var mergedTables = [
    "mls_car_master_records",
    "reso_crea_merged",
    "bridge_bcre_merged",
    "mls_rae_master_records",
    "reso_treb_evow_merged"
];

mergedTables.forEach(function(tableName) {
    db[tableName].updateMany(
        { downloadStatus: { $exists: false } },
        { $set: { downloadStatus: {} } }
    );

    // 创建索引
    db[tableName].createIndex({ "downloadStatus": 1 });
    db[tableName].createIndex({ "phoP": 1 }); // 为phoP创建索引
});
```

### 3.4 测试验证

#### 3.4.1 单元测试
创建 `goresodownload/resource_download_queue_test.go`:

```go
func TestGetNext_MultiHost(t *testing.T) {
    // 测试多主机队列处理
    // 1. ca6获取任务后，ca7仍能获取同一任务
    // 2. 两个主机都处理完成后，任务被删除
}
```

创建 `golevelstore/host_filter_test.go`:

```go
package levelStore

import (
    "testing"
    "github.com/stretchr/testify/assert"
)

func TestHostStorageFilter_FilterStoragePaths(t *testing.T) {
    tests := []struct {
        name     string
        hostname string
        paths    []string
        expected []string
    }{
        {
            name:     "ca6 host filters ca6 paths",
            hostname: "ca6",
            paths:    []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
            expected: []string{"/mnt/ca6m0/imgs/MLS/TRB"},
        },
        {
            name:     "ca7 host filters ca7 paths",
            hostname: "ca7",
            paths:    []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
            expected: []string{"/mnt/ca7m0/imgs/MLS/TRB"},
        },
        {
            name:     "other host returns all paths",
            hostname: "other",
            paths:    []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
            expected: []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            filter := &HostStorageFilter{hostname: tt.hostname}
            result := filter.FilterStoragePaths(tt.paths)
            assert.Equal(t, tt.expected, result)
        })
    }
}
```

## 4. 部署方案

### 4.1 部署步骤
1. **数据库迁移**: 先执行数据库迁移脚本，创建独立队列和状态字段
2. **代码部署**: 将修改后的代码部署到ca6和ca7服务器
3. **服务重启**: 重启goresodownload服务使新配置生效
4. **验证测试**: 检查日志确认独立队列和路径过滤正常工作

### 4.2 验证方法
1. **队列检查**: 确认ca6和ca7使用不同的队列集合
2. **路径验证**: 查看日志中的"Filtered storage paths for host"信息
3. **状态跟踪**: 检查merged表中的downloadStatus字段更新
4. **数据一致性**: 验证两边下载的文件完全一致

### 4.3 监控要点
1. **队列状态**: 监控两个队列的任务数量和处理速度
2. **下载状态**: 通过downloadStatus字段监控哪些property已完成下载
3. **存储使用**: 观察两个盘的存储使用情况
4. **错误日志**: 关注路径过滤和队列处理的错误信息

### 4.4 回滚方案
如果出现问题，可以：
1. 回滚代码到原始版本
2. 恢复使用原始队列
3. 清理新创建的队列集合和状态字段

## 5. 风险评估

### 5.1 中等风险
- **数据库结构变更**: 需要添加新字段和索引
- **队列逻辑复杂**: 多主机处理逻辑相对复杂
- **向后兼容**: 非ca6/ca7主机行为保持不变

### 5.2 注意事项
- **数据库迁移**: 需要先执行数据库迁移脚本
- **主机名依赖**: 确保ca6/ca7服务器主机名设置正确
- **队列监控**: 监控队列处理状态，确保任务不会积压
- **数据一致性**: 验证两边下载的数据完全一致

## 6. 状态查询工具

### 6.1 创建状态查询脚本
创建 `goresodownload/cmd/batch/checkDownloadStatus/main.go`:

```go
// 查询指定property的下载状态
func checkPropertyStatus(propertyID string, board string) {
    // 查询merged表中的downloadStatus
    collectionName := goresodownload.BoardMergedTable[board]
    coll := gomongo.Coll("rni", collectionName)

    var doc bson.M
    err := coll.FindOne(context.Background(), bson.M{"_id": propertyID}).Decode(&doc)
    if err != nil {
        fmt.Printf("Property %s not found: %v\n", propertyID, err)
        return
    }

    downloadStatus, exists := doc["downloadStatus"]
    if !exists {
        fmt.Printf("Property %s: No download status\n", propertyID)
        return
    }

    fmt.Printf("Property %s download status:\n", propertyID)
    if statusMap, ok := downloadStatus.(map[string]interface{}); ok {
        for host, status := range statusMap {
            fmt.Printf("  %s: %v\n", host, status)
        }
    }

    // 检查是否两边都完成
    ca6Done := false
    ca7Done := false
    if statusMap, ok := downloadStatus.(map[string]interface{}); ok {
        if val, exists := statusMap["ca6"]; exists {
            ca6Done, _ = val.(bool)
        }
        if val, exists := statusMap["ca7"]; exists {
            ca7Done, _ = val.(bool)
        }
    }

    if ca6Done && ca7Done {
        fmt.Printf("✅ Both ca6 and ca7 completed\n")
    } else {
        fmt.Printf("⏳ Pending: ca6=%v, ca7=%v\n", ca6Done, ca7Done)
    }
}
```

### 6.2 队列监控脚本
```go
// 监控队列状态
func monitorQueues() {
    queues := []string{
        "resource_download_queue",
        "resource_download_queue_ca6",
        "resource_download_queue_ca7",
    }

    for _, queueName := range queues {
        coll := gomongo.Coll("rni", queueName)
        count, _ := coll.CountDocuments(context.Background(), bson.M{})
        fmt.Printf("Queue %s: %d items\n", queueName, count)
    }
}
```

## 7. 后续优化

### 7.1 可选增强
- **自动同步检查**: 定期检查两边数据一致性的脚本
- **队列平衡**: 监控队列积压情况，自动调整处理优先级
- **状态报告**: 生成下载完成状态的定期报告

### 7.2 长期规划
- **增量同步**: 支持增量数据同步，减少重复下载
- **容灾切换**: 在一个盘故障时自动切换到另一个盘
