# Flexible Email Engine Configuration (INI Format)
# Each [[emailEngine]] section defines one engine with nameKey and type

[contact]
defaultEmail = "<EMAIL>"
defaultEmailFromName = "Your Company"

[dbs]
verbose = 3

[dbs.tmp]
uri = "mongodb://localhost:27017/test"

[golog]
dir = "/tmp/logs"
level = "info"

# Global email engine configuration
[emailEngineConfig]
defaultEngine = "gmail-primary"
fallbackStrategy = "priority"
retryCount = 3
retryDelay = 1000
enableFailover = true
healthCheckInterval = 300

# Email Engine Configurations
# Each engine has nameKey (for external calls) and type (engine type)

# Gmail Primary SMTP
[[emailEngine]]
nameKey = "gmail-primary"
type = "smtp"
enabled = true
defaultEmail = "<EMAIL>"
priority = 1
rateLimit = 250
# SMTP fields
host = "smtp.gmail.com"
port = 587
user = "<EMAIL>"
password = "your-app-password"
tls = true
startTLS = true

# Gmail Secondary SMTP
[[emailEngine]]
nameKey = "gmail-backup"
type = "smtp"
enabled = true
defaultEmail = "<EMAIL>"
priority = 3
rateLimit = 300
# SMTP fields
host = "smtp.gmail.com"
port = 587
user = "<EMAIL>"
password = "your-app-password-2"
tls = true
startTLS = true

# Outlook Business SMTP
[[emailEngine]]
nameKey = "outlook-business"
type = "smtp"
enabled = true
defaultEmail = "<EMAIL>"
priority = 2
rateLimit = 250
# SMTP fields
host = "smtp-mail.outlook.com"
port = 587
user = "<EMAIL>"
password = "your-password"
tls = true
startTLS = true

# QQ Mail SMTP
[[emailEngine]]
nameKey = "qq-mail"
type = "smtp"
enabled = true
defaultEmail = "<EMAIL>"
priority = 4
rateLimit = 500
# SMTP fields
host = "smtp.qq.com"
port = 587
user = "<EMAIL>"
password = "your-authorization-code"
tls = true
startTLS = true

# Company SMTP Server
[[emailEngine]]
nameKey = "company-smtp"
type = "smtp"
enabled = true
defaultEmail = "<EMAIL>"
priority = 2
rateLimit = 200
# SMTP fields
host = "mail.company.com"
port = 465
user = "<EMAIL>"
password = "company-password"
tls = true
ssl = true

# AWS SES Primary
[[emailEngine]]
nameKey = "aws-ses-primary"
type = "ses"
enabled = true
defaultEmail = "<EMAIL>"
priority = 1
rateLimit = 100
# SES fields
region = "us-east-1"
accessKeyId = "********************"
secretAccessKey = "Uogp6eMqmbIhxB43mqCYPJYBT9x3vnQZwrawvz6W"

# AWS SES Backup
[[emailEngine]]
nameKey = "aws-ses-backup"
type = "ses"
enabled = true
defaultEmail = "<EMAIL>"
priority = 5
rateLimit = 150
# SES fields
region = "us-west-2"
accessKeyId = "********************"
secretAccessKey = "NqYJDdVhIMdvVqlJnmBSIBsbHKCEQLlkw91/v4rA"

# RealMaster Mail Service
[[emailEngine]]
nameKey = "rm-mail-service"
type = "rmMail"
enabled = true
defaultEmail = "<EMAIL>"
priority = 3
rateLimit = 300
# RMMail fields
url = "https://ml1.realmaster.cc/send"
apiKey = "your-api-key"
timeout = 30

# SendGrid (disabled by default)
[[emailEngine]]
nameKey = "sendgrid-marketing"
type = "sendgrid"
enabled = false
defaultEmail = "<EMAIL>"
priority = 4
rateLimit = 200
# SendGrid fields
apiKey = "SG.your-sendgrid-api-key"
templateEngine = true

# Local Sendmail
[[emailEngine]]
nameKey = "local-sendmail"
type = "sendmail"
enabled = true
defaultEmail = "system@localhost"
priority = 6
rateLimit = 1000
# Sendmail fields
path = "/usr/sbin/sendmail"
args = ["-t", "-i"]

# Mock Engine for Testing
[[emailEngine]]
nameKey = "mock-testing"
type = "mock"
enabled = false
defaultEmail = "<EMAIL>"
priority = 10
rateLimit = 0
# Mock fields
verbose = true
logFile = "/tmp/mock-emails.log"
saveToFile = true
