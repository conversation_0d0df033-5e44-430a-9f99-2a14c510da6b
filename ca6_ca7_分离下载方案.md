# CA6/CA7 备份下载方案

## 1. 需求分析

### 1.1 当前架构
- **存储配置**: 每个board类型配置了两个存储路径，分别对应ca6和ca7两个盘
- **下载逻辑**: 当前下载器会同时写入所有配置的存储路径（冗余存储），写挂载盘的时候，速度很慢
- **配置示例**:
```ini
[imageStore]
reso_treb_image_dir = ["/mnt/ca6m0/imgs/MLS/TRB","/mnt/ca7m0/imgs/MLS/TRB"]
reso_car_image_dir = ["/mnt/ca6m0/imgs/MLS/CAR","/mnt/ca7m0/imgs/MLS/CAR"]
```

### 1.2 备份需求
- **ca6服务器**: 运行完整的下载服务，但只下载到ca6路径，形成完整的数据备份
- **ca7服务器**: 运行完整的下载服务，但只下载到ca7路径，形成完整的数据备份
- **目的**: 两套完整的独立数据备份，用于容灾和数据安全

### 1.3 实际架构
- **ca6服务器**: 运行完整服务栈（watch → queue_ca6 → downloader → ca6存储）
- **ca7服务器**: 运行完整服务栈（watch → queue_ca7 → downloader → ca7存储）
- **独立运行**: 两套服务完全独立，各自watch变化并添加到各自队列

### 1.4 核心问题
1. **路径竞态**: ca6和ca7的watch可能在不同时间触发，计算出不同的phoP路径
2. **时序差异**: property变化时，两边可能看到不同的数据状态
3. **数据覆盖**: 旧的处理结果可能覆盖新的处理结果
   - 例如：ca6处理propA(20张图) → propA变成30张 → ca7处理propA(30张) → ca6完成后覆盖成20张
4. **目标**: 确保两边下载到相同的路径，数据完全一致，避免旧数据覆盖新数据

## 2. 技术方案

### 2.1 方案选择
为了确保两边数据完全一致，有以下方案：

**方案A: 修改队列机制** - 允许多个服务器处理同一个任务
**方案B: 独立队列** - ca6和ca7使用独立的队列
**方案C: 主从复制** - 一个主服务器处理，另一个从服务器复制

**推荐方案B**: 独立队列，逻辑清晰，易于管理

### 2.2 核心实现思路
1. **独立队列**: ca6和ca7使用不同的队列集合（`queue_ca6`, `queue_ca7`）
2. **路径协调**: 通过merged表作为"路径协调中心"，确保两边使用相同路径
3. **版本控制**: 在队列中记录property的版本信息，防止旧数据覆盖新数据
4. **条件更新**: 只有当版本号不低于当前版本时才允许更新
5. **原子操作**: 使用条件更新确保数据一致性
6. **存储过滤**: 根据主机名只写入对应的存储路径

## 3. 实现方案

### 3.1 修改文件清单

#### 3.1.1 核心修改文件
- `goresodownload/resource_download_queue.go` - 修改GetNext函数，支持独立队列
- `goresodownload/downloader.go` - 修改merged表更新逻辑，添加路径协调机制
- `golevelstore/level_store_helper.go` - 修改GetImageDir函数，添加主机名过滤
- `golevelstore/host_filter.go` - 新增主机名过滤逻辑（新文件）

#### 3.1.2 数据库变更
- 创建独立的队列集合：`resource_download_queue_ca6`, `resource_download_queue_ca7`
- 队列表添加版本字段：`propVersion`
- merged表添加版本控制字段：`lastProcessedVersion`, `lastProcessedBy`, `lastProcessedAt`
- merged表添加下载完成状态字段：`downloadStatus`

#### 3.1.3 配置变更
- 需要配置参数区分ca6和ca7主机

#### 3.1.3 配置文件
- 生产环境配置文件 - 无需修改，保持现有配置

### 3.2 详细实现步骤

#### 步骤1: 修改队列初始化，支持独立队列
在 `goresodownload/resource_download_queue.go` 中修改:

```go
// ResourceDownloadQueue manages the download queue operations
type ResourceDownloadQueue struct {
    queueCol *gomongo.MongoCollection
    hostname string // 新增：当前主机名
}

// NewResourceDownloadQueue creates a new ResourceDownloadQueue instance
func NewResourceDownloadQueue(queueCol *gomongo.MongoCollection) *ResourceDownloadQueue {
    hostname, err := os.Hostname()
    if err != nil {
        golog.Warn("Failed to get hostname", "error", err)
        hostname = ""
    }

    return &ResourceDownloadQueue{
        queueCol: queueCol,
        hostname: hostname,
    }
}

// getQueueCollectionName 根据主机名返回对应的队列集合名
func (q *ResourceDownloadQueue) getQueueCollectionName() string {
    switch q.hostname {
    case "ca6":
        return "resource_download_queue_ca6"
    case "ca7":
        return "resource_download_queue_ca7"
    default:
        return "resource_download_queue" // 默认队列
    }
}
```

#### 步骤2: 修改AddToQueue函数，添加版本控制
```go
// AddToQueue adds a resource to the download queue with version control
func (q *ResourceDownloadQueue) AddToQueue(id string, priority int, src string) error {
    // Parse default download start time
    defaultStartTime, err := time.Parse(time.RFC3339, DEFAULT_DOWNLOAD_START_TS)
    if err != nil {
        return fmt.Errorf("failed to parse default start time: %w", err)
    }

    // 获取当前property的版本信息（使用_mt作为版本标识）
    collectionName := goresodownload.BoardMergedTable[src]
    mergedCol := gomongo.Coll("rni", collectionName)

    var propDoc bson.M
    err = mergedCol.FindOne(context.Background(), bson.M{"_id": id}).Decode(&propDoc)
    if err != nil {
        return fmt.Errorf("failed to get property document: %w", err)
    }

    // 使用property的_mt作为版本标识
    var propVersion time.Time
    if mt, exists := propDoc["_mt"]; exists {
        if mtTime, ok := mt.(time.Time); ok {
            propVersion = mtTime
        }
    }

    // Create update document with version info
    ctx := context.Background()
    filter := bson.M{"_id": id}
    updateDoc := bson.M{
        "_id":          id,
        "src":          src,
        "_mt":          time.Now(),
        "dlShallEndTs": defaultStartTime,
        "priority":     priority,
        "propVersion":  propVersion,  // 新增：property版本
    }

    // 根据主机名使用对应的队列
    queueCollectionName := q.getQueueCollectionName()
    targetCol := gomongo.Coll("rni", queueCollectionName)

    _, err = targetCol.ReplaceOne(ctx, filter, updateDoc, options.Replace().SetUpsert(true))
    if err != nil {
        return fmt.Errorf("failed to upsert to queue %s: %w", queueCollectionName, err)
    }

    golog.Info("Added to queue with version",
        "id", id,
        "priority", priority,
        "propVersion", propVersion,
        "hostname", q.hostname,
        "queueCollection", queueCollectionName)

    return nil
}
```

#### 步骤3: 修改GetNext函数使用独立队列
```go
// GetNext gets the next single resource to download for a specific board
func (q *ResourceDownloadQueue) GetNext(boardType string) (*QueueItem, error) {
    ctx := context.Background()

    // 根据主机名使用对应的队列集合
    queueCollectionName := q.getQueueCollectionName()
    targetCol := gomongo.Coll("rni", queueCollectionName)

    // Filter: items with dlShallEndTs in the past and matching boardType
    filter := bson.M{
        "src":          boardType,
        "dlShallEndTs": bson.M{"$lt": time.Now()},
    }

    // Update: set dlShallEndTs to current time + DOWNLOAD_ALLOWED_MS
    newDlShallEndTs := time.Now().Add(time.Duration(DOWNLOAD_ALLOWED_MS) * time.Millisecond)
    update := bson.M{
        "$set": bson.M{
            "dlShallEndTs": newDlShallEndTs,
        },
    }

    // Options: sort by priority descending (highest priority first)
    opts := options.FindOneAndUpdate().
        SetSort(bson.M{"priority": -1}).
        SetReturnDocument(options.After)

    var result QueueItem
    err := targetCol.FindOneAndUpdate(ctx, filter, update, opts).Decode(&result)
    if err != nil {
        if err == mongo.ErrNoDocuments {
            return nil, nil // No items available
        }
        return nil, fmt.Errorf("failed to find and update queue item: %w", err)
    }

    golog.Info("Retrieved queue item",
        "itemID", result.ID,
        "hostname", q.hostname,
        "queueCollection", queueCollectionName)

    return &result, nil
}
```

#### 步骤4: 修改下载器处理逻辑，实现路径协调
在 `goresodownload/downloader.go` 中修改updateMergedDoc函数:

```go
// updateMergedDocWithVersion updates the merged document with version control
func (d *Downloader) updateMergedDocWithVersion(result *AnalysisResult, thumbnailHash int32, board string, queueItem *QueueItem) error {
    if result == nil {
        return fmt.Errorf("result is nil")
    }

    if d.MergedCol == nil {
        golog.Warn("MergedCol is nil, skipping document update", "ID", result.ID)
        return nil
    }

    ctx := context.Background()
    hostname, _ := os.Hostname()

    // 第一步：尝试设置phoP路径（路径协调）
    if hostname == "ca6" || hostname == "ca7" {
        err := d.coordinatePhoPath(result.ID, board)
        if err != nil {
            golog.Warn("Failed to coordinate phoP path", "error", err, "ID", result.ID)
            // 继续处理，不因为路径协调失败而中断
        }
    }

    // 第二步：版本控制更新
    // 只有当队列中的版本不低于merged表中的版本时才允许更新
    filter := bson.M{
        "_id": result.ID,
        "$or": []bson.M{
            {"lastProcessedVersion": bson.M{"$exists": false}},  // 第一次处理
            {"lastProcessedVersion": bson.M{"$lte": queueItem.PropVersion}}, // 版本不低于当前版本
        },
    }

    update := bson.M{"$set": bson.M{}}
    unsetFields := bson.M{}

    // 更新版本信息
    update["$set"].(bson.M)["lastProcessedVersion"] = queueItem.PropVersion
    update["$set"].(bson.M)["lastProcessedBy"] = hostname
    update["$set"].(bson.M)["lastProcessedAt"] = time.Now()

    // Handle PhoLH
    if len(result.PhoLH) > 0 {
        update["$set"].(bson.M)["phoLH"] = result.PhoLH
    } else {
        unsetFields["phoLH"] = ""
    }

    // Handle DocLH
    if len(result.DocLH) > 0 {
        update["$set"].(bson.M)["docLH"] = result.DocLH
    } else {
        unsetFields["docLH"] = ""
    }

    // Add thumbnail hash if available
    if thumbnailHash != 0 {
        update["$set"].(bson.M)["tnLH"] = thumbnailHash
    } else {
        unsetFields["tnLH"] = ""
    }

    // 更新下载状态
    if hostname == "ca6" || hostname == "ca7" {
        update["$set"].(bson.M)[fmt.Sprintf("downloadStatus.%s", hostname)] = true
    }

    // Only add $unset to update if there are fields to unset
    if len(unsetFields) > 0 {
        update["$unset"] = unsetFields
    }

    // 执行条件更新操作
    updateResult, err := d.MergedCol.UpdateOne(ctx, filter, update)
    if err != nil {
        golog.Error("Failed to update merged document", "error", err, "ID", result.ID)
        return err
    }

    if updateResult.ModifiedCount == 0 {
        golog.Warn("Document not updated - version conflict",
            "ID", result.ID,
            "hostname", hostname,
            "queueVersion", queueItem.PropVersion)
        return fmt.Errorf("version conflict: document not updated")
    }

    golog.Info("Successfully updated merged document with version control",
        "ID", result.ID,
        "hostname", hostname,
        "queueVersion", queueItem.PropVersion,
        "modifiedCount", updateResult.ModifiedCount)

    return nil
}

// 保持原有函数用于向后兼容
func (d *Downloader) updateMergedDoc(result *AnalysisResult, thumbnailHash int32, board string) error {
    // 对于没有版本信息的情况，创建一个默认的队列项
    queueItem := &QueueItem{
        PropVersion: time.Now(), // 使用当前时间作为默认版本
    }
    return d.updateMergedDocWithVersion(result, thumbnailHash, board, queueItem)
}

// coordinatePhoPath 协调phoP路径，确保两边使用相同路径
func (d *Downloader) coordinatePhoPath(propertyID string, board string) error {
    ctx := context.Background()

    // 首先检查是否已有phoP路径
    var existingDoc bson.M
    err := d.MergedCol.FindOne(ctx, bson.M{"_id": propertyID}).Decode(&existingDoc)
    if err != nil && err.Error() != "mongo: no documents in result" {
        return fmt.Errorf("failed to check existing document: %w", err)
    }

    // 如果已有phoP路径，直接返回
    if existingPhoP, exists := existingDoc["phoP"]; exists && existingPhoP != nil {
        if phoPStr, ok := existingPhoP.(string); ok && phoPStr != "" {
            golog.Debug("phoP already exists, using existing path",
                "ID", propertyID,
                "existingPhoP", phoPStr)
            return nil
        }
    }

    // 计算新的phoP路径
    propTs, err := goresodownload.GetPropTsForPath(existingDoc, board)
    if err != nil {
        return fmt.Errorf("failed to get PropTs for path: %w", err)
    }

    var listingKey string
    if board == "TRB" {
        listingKey, _ = existingDoc["ListingKey"].(string)
    } else {
        listingKey, _ = existingDoc["ListingId"].(string)
    }

    phoP, err := levelStore.GetFullFilePathForProp(propTs, board, listingKey)
    if err != nil {
        return fmt.Errorf("failed to get full file path: %w", err)
    }

    // 使用原子操作设置phoP路径（只在不存在时设置）
    filter := bson.M{
        "_id": propertyID,
        "$or": []bson.M{
            {"phoP": bson.M{"$exists": false}},
            {"phoP": ""},
            {"phoP": nil},
        },
    }

    update := bson.M{
        "$set": bson.M{"phoP": phoP},
    }

    result, err := d.MergedCol.UpdateOne(ctx, filter, update)
    if err != nil {
        return fmt.Errorf("failed to set phoP path: %w", err)
    }

    if result.ModifiedCount > 0 {
        golog.Info("Set phoP path for property",
            "ID", propertyID,
            "phoP", phoP,
            "hostname", os.Hostname())
    } else {
        golog.Debug("phoP path already set by another process",
            "ID", propertyID)
    }

    return nil
}
```

#### 步骤5: 创建主机存储过滤模块
创建新文件 `golevelstore/host_filter.go`:

```go
package levelStore

import (
    "os"
    "strings"
    golog "github.com/real-rm/golog"
)

// HostStorageFilter 主机存储路径过滤器
type HostStorageFilter struct {
    hostname string
}

// NewHostStorageFilter 创建主机存储过滤器
func NewHostStorageFilter() *HostStorageFilter {
    hostname, err := os.Hostname()
    if err != nil {
        golog.Warn("Failed to get hostname, using empty hostname", "error", err)
        hostname = ""
    }
    return &HostStorageFilter{hostname: hostname}
}

// FilterStoragePaths 根据主机名过滤存储路径
func (f *HostStorageFilter) FilterStoragePaths(paths []string) []string {
    if f.hostname == "" {
        return paths // 无法获取主机名时返回所有路径
    }

    // 定义主机名到路径前缀的映射
    hostPathMap := map[string]string{
        "ca6": "ca6",
        "ca7": "ca7",
    }

    // 检查是否为需要过滤的主机
    pathPrefix, needFilter := hostPathMap[f.hostname]
    if !needFilter {
        return paths // 非ca6/ca7主机，返回所有路径
    }

    // 过滤路径
    var filteredPaths []string
    for _, path := range paths {
        if strings.Contains(path, pathPrefix) {
            filteredPaths = append(filteredPaths, path)
        }
    }

    if len(filteredPaths) == 0 {
        golog.Warn("No matching storage paths found for host",
            "hostname", f.hostname,
            "pathPrefix", pathPrefix,
            "originalPaths", paths)
        return paths // 如果没有匹配的路径，返回原始路径避免服务异常
    }

    golog.Info("Filtered storage paths for host",
        "hostname", f.hostname,
        "originalCount", len(paths),
        "filteredCount", len(filteredPaths),
        "filteredPaths", filteredPaths)

    return filteredPaths
}
```

#### 步骤6: 修改GetImageDir函数
在 `golevelstore/level_store_helper.go` 中修改GetImageDir函数:

```go
// 在文件顶部添加全局过滤器变量
var globalHostFilter *HostStorageFilter

func init() {
    // 初始化主机过滤器
    globalHostFilter = NewHostStorageFilter()
}

// 修改GetImageDir函数，在返回前应用过滤器
var GetImageDir = func(board string) ([]string, error) {
    // ... 现有的获取路径逻辑保持不变 ...

    // 在返回前应用主机过滤器
    if len(dirs) > 0 && globalHostFilter != nil {
        dirs = globalHostFilter.FilterStoragePaths(dirs)
    }

    golog.Info("GetImageDir", "board", board, "dirs", dirs)
    return dirs, nil
}
```

### 3.3 数据库迁移

#### 3.3.1 创建独立队列集合
```javascript
// MongoDB 迁移脚本

// 1. 创建ca6队列集合
db.createCollection("resource_download_queue_ca6");

// 2. 创建ca7队列集合
db.createCollection("resource_download_queue_ca7");

// 3. 复制现有队列数据到新集合
db.resource_download_queue.find().forEach(function(doc) {
    db.resource_download_queue_ca6.insert(doc);
    db.resource_download_queue_ca7.insert(doc);
});

// 4. 创建索引以提高查询性能
db.resource_download_queue_ca6.createIndex({
    "src": 1,
    "dlShallEndTs": 1,
    "priority": -1
});

db.resource_download_queue_ca7.createIndex({
    "src": 1,
    "dlShallEndTs": 1,
    "priority": -1
});
```

#### 3.3.2 为队列表添加新字段
```javascript
// 为新队列表添加必要字段的索引
db.resource_download_queue_ca6.createIndex({
    "src": 1,
    "dlShallEndTs": 1,
    "priority": -1,
    "phoP": 1
});

db.resource_download_queue_ca7.createIndex({
    "src": 1,
    "dlShallEndTs": 1,
    "priority": -1,
    "phoP": 1
});
```

#### 3.3.3 为merged表添加版本控制和状态字段
```javascript
// 为所有merged表添加版本控制和downloadStatus字段
var mergedTables = [
    "mls_car_master_records",
    "reso_crea_merged",
    "bridge_bcre_merged",
    "mls_rae_master_records",
    "reso_treb_evow_merged"
];

mergedTables.forEach(function(tableName) {
    db[tableName].updateMany(
        {
            $or: [
                { downloadStatus: { $exists: false } },
                { lastProcessedVersion: { $exists: false } }
            ]
        },
        {
            $set: {
                downloadStatus: {},
                lastProcessedVersion: null,
                lastProcessedBy: null,
                lastProcessedAt: null
            }
        }
    );

    // 创建索引
    db[tableName].createIndex({ "downloadStatus": 1 });
    db[tableName].createIndex({ "phoP": 1 });
    db[tableName].createIndex({ "lastProcessedVersion": 1 });
    db[tableName].createIndex({ "lastProcessedBy": 1 });

    // 创建复合索引用于查询完成状态
    db[tableName].createIndex({
        "downloadStatus.ca6": 1,
        "downloadStatus.ca7": 1
    });

    // 创建复合索引用于版本控制查询
    db[tableName].createIndex({
        "_id": 1,
        "lastProcessedVersion": 1
    });
});
```

### 3.4 测试验证

#### 3.4.1 单元测试
创建 `goresodownload/resource_download_queue_test.go`:

```go
func TestGetNext_MultiHost(t *testing.T) {
    // 测试多主机队列处理
    // 1. ca6获取任务后，ca7仍能获取同一任务
    // 2. 两个主机都处理完成后，任务被删除
}
```

创建 `golevelstore/host_filter_test.go`:

```go
package levelStore

import (
    "testing"
    "github.com/stretchr/testify/assert"
)

func TestHostStorageFilter_FilterStoragePaths(t *testing.T) {
    tests := []struct {
        name     string
        hostname string
        paths    []string
        expected []string
    }{
        {
            name:     "ca6 host filters ca6 paths",
            hostname: "ca6",
            paths:    []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
            expected: []string{"/mnt/ca6m0/imgs/MLS/TRB"},
        },
        {
            name:     "ca7 host filters ca7 paths",
            hostname: "ca7",
            paths:    []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
            expected: []string{"/mnt/ca7m0/imgs/MLS/TRB"},
        },
        {
            name:     "other host returns all paths",
            hostname: "other",
            paths:    []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
            expected: []string{"/mnt/ca6m0/imgs/MLS/TRB", "/mnt/ca7m0/imgs/MLS/TRB"},
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            filter := &HostStorageFilter{hostname: tt.hostname}
            result := filter.FilterStoragePaths(tt.paths)
            assert.Equal(t, tt.expected, result)
        })
    }
}
```

## 4. 部署方案

### 4.1 部署步骤
1. **数据库迁移**: 先执行数据库迁移脚本，创建独立队列和状态字段
2. **代码部署**: 将修改后的代码部署到ca6和ca7服务器
3. **服务重启**: 重启goresodownload服务使新配置生效
4. **验证测试**: 检查日志确认独立队列和路径过滤正常工作

### 4.2 验证方法
1. **队列检查**: 确认ca6和ca7使用不同的队列集合
2. **路径验证**: 查看日志中的"Filtered storage paths for host"信息
3. **状态跟踪**: 检查merged表中的downloadStatus字段更新
4. **数据一致性**: 验证两边下载的文件完全一致

### 4.3 监控要点
1. **队列状态**: 监控两个队列的任务数量和处理速度
2. **下载状态**: 通过downloadStatus字段监控哪些property已完成下载
3. **存储使用**: 观察两个盘的存储使用情况
4. **错误日志**: 关注路径过滤和队列处理的错误信息

### 4.4 回滚方案
如果出现问题，可以：
1. 回滚代码到原始版本
2. 恢复使用原始队列
3. 清理新创建的队列集合和状态字段

## 5. 风险评估

### 5.1 中等风险
- **数据库结构变更**: 需要添加新字段和索引
- **队列逻辑复杂**: 多主机处理逻辑相对复杂
- **向后兼容**: 非ca6/ca7主机行为保持不变

### 5.2 注意事项
- **数据库迁移**: 需要先执行数据库迁移脚本
- **主机名依赖**: 确保ca6/ca7服务器主机名设置正确
- **队列监控**: 监控队列处理状态，确保任务不会积压
- **数据一致性**: 验证两边下载的数据完全一致

## 6. 简单查询工具

### 6.1 基本查询脚本
创建 `goresodownload/cmd/batch/checkDownloadStatus/main.go`:

```go
package main

import (
    "context"
    "fmt"
    "os"

    "github.com/real-rm/gomongo"
    "github.com/real-rm/goresodownload"
    "go.mongodb.org/mongo-driver/bson"
)

// checkPropertyStatus 查询指定property的下载状态
func checkPropertyStatus(propertyID string, board string) {
    collectionName := goresodownload.BoardMergedTable[board]
    coll := gomongo.Coll("rni", collectionName)

    var doc bson.M
    err := coll.FindOne(context.Background(), bson.M{"_id": propertyID}).Decode(&doc)
    if err != nil {
        fmt.Printf("Property %s not found: %v\n", propertyID, err)
        return
    }

    // 获取phoP路径
    phoP := ""
    if phoPVal, exists := doc["phoP"]; exists && phoPVal != nil {
        phoP, _ = phoPVal.(string)
    }

    // 解析下载状态
    ca6Done := false
    ca7Done := false
    if downloadStatus, exists := doc["downloadStatus"]; exists {
        if statusMap, ok := downloadStatus.(map[string]interface{}); ok {
            if val, exists := statusMap["ca6"]; exists {
                ca6Done, _ = val.(bool)
            }
            if val, exists := statusMap["ca7"]; exists {
                ca7Done, _ = val.(bool)
            }
        }
    }

    // 输出结果
    fmt.Printf("Property: %s\n", propertyID)
    fmt.Printf("PhoP Path: %s\n", phoP)
    fmt.Printf("CA6 Status: %s\n", getStatusString(ca6Done))
    fmt.Printf("CA7 Status: %s\n", getStatusString(ca7Done))

    if ca6Done && ca7Done {
        fmt.Printf("✅ Both servers completed\n")
    } else {
        fmt.Printf("⏳ Pending: ca6=%v, ca7=%v\n", ca6Done, ca7Done)
    }
    fmt.Println()
}

func getStatusString(completed bool) string {
    if completed {
        return "✅ Completed"
    }
    return "❌ Not completed"
}

func main() {
    if len(os.Args) < 3 {
        fmt.Println("Usage: checkDownloadStatus <propertyID> <board>")
        fmt.Println("Example: checkDownloadStatus prop123 TRB")
        os.Exit(1)
    }

    propertyID := os.Args[1]
    board := os.Args[2]

    checkPropertyStatus(propertyID, board)
}
```

### 6.2 常用MongoDB查询
```javascript
// 1. 查询两边都完成的properties数量
db.reso_treb_evow_merged.countDocuments({
    "downloadStatus.ca6": true,
    "downloadStatus.ca7": true
});

// 2. 查询只有ca6完成的properties
db.reso_treb_evow_merged.find({
    "downloadStatus.ca6": true,
    "downloadStatus.ca7": {$ne: true}
}).limit(10);

// 3. 查询只有ca7完成的properties
db.reso_treb_evow_merged.find({
    "downloadStatus.ca7": true,
    "downloadStatus.ca6": {$ne: true}
}).limit(10);

// 4. 查询都没完成的properties
db.reso_treb_evow_merged.find({
    "downloadStatus.ca6": {$ne: true},
    "downloadStatus.ca7": {$ne: true}
}).limit(10);

// 5. 统计各种状态的数量
db.reso_treb_evow_merged.aggregate([
    {
        $project: {
            ca6Done: {$ifNull: ["$downloadStatus.ca6", false]},
            ca7Done: {$ifNull: ["$downloadStatus.ca7", false]}
        }
    },
    {
        $group: {
            _id: {ca6Done: "$ca6Done", ca7Done: "$ca7Done"},
            count: {$sum: 1}
        }
    }
]);
```

## 7. 总结

### 7.1 核心改动
1. **独立队列**: ca6和ca7使用各自的队列集合
2. **路径协调**: 通过原子操作确保使用相同的phoP路径
3. **状态跟踪**: 简单的downloadStatus字段记录完成状态
4. **存储过滤**: 根据主机名只写入对应的存储路径

### 7.2 数据结构
```javascript
// merged表中的字段
{
    "_id": "property123",
    "phoP": "/mnt/ca6m0/imgs/MLS/TRB/L1/L2/property123",  // 原子设置的路径
    "downloadStatus": {
        "ca6": true,    // ca6完成状态
        "ca7": true     // ca7完成状态
    },
    "lastProcessedVersion": ISODate("2024-01-01T10:30:00Z"), // 最后处理的版本
    "lastProcessedBy": "ca7",                                // 最后处理的主机
    "lastProcessedAt": ISODate("2024-01-01T10:35:00Z")      // 最后处理时间
}

// 队列表中的字段
{
    "_id": "property123",
    "src": "TRB",
    "propVersion": ISODate("2024-01-01T10:30:00Z"),  // property版本（来自merged表的_mt）
    "priority": 1,
    "dlShallEndTs": ISODate("2024-01-01T09:00:00Z")
}
```

### 7.3 查询方式
```bash
# 检查单个property
./checkDownloadStatus property123 TRB

# MongoDB直接查询
db.reso_treb_evow_merged.find({"_id": "property123"})
```

### 7.4 版本控制机制
```
时间线示例：
T1: propA有20张图，_mt=2024-01-01T10:00:00Z
T2: ca6开始处理propA，队列中propVersion=2024-01-01T10:00:00Z
T3: propA变成30张图，_mt=2024-01-01T10:30:00Z
T4: ca7开始处理propA，队列中propVersion=2024-01-01T10:30:00Z
T5: ca7完成，更新merged表：lastProcessedVersion=2024-01-01T10:30:00Z，phoLH=30张
T6: ca6完成，尝试更新但版本检查失败（2024-01-01T10:00:00Z < 2024-01-01T10:30:00Z）
T7: ca6的更新被拒绝，merged表保持30张图的正确结果
```

### 7.5 优势
- ✅ **版本控制**: 防止旧数据覆盖新数据
- ✅ **路径一致**: 原子操作保证路径统一
- ✅ **完全独立**: 两套服务独立运行
- ✅ **数据安全**: 条件更新确保数据正确性
- ✅ **易于调试**: 记录最后处理的主机和时间
