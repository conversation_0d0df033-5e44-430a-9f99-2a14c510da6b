package gomail

import (
	"fmt"
	"sort"
	"strings"

	goconfig "github.com/real-rm/goconfig"
	golog "github.com/real-rm/golog"
)

// EmailEngineConfig represents a single email engine configuration
type EmailEngineConfig struct {
	NameKey      string `toml:"nameKey"`      // External identifier for this engine
	Type         string `toml:"type"`         // Engine type: smtp, ses, rmMail, sendgrid, sendmail, mock
	Enabled      bool   `toml:"enabled"`      // Whether this engine is enabled
	DefaultEmail string `toml:"defaultEmail"` // Default from email for this engine
	Priority     int    `toml:"priority"`     // Priority (lower number = higher priority)
	RateLimit    int    `toml:"rateLimit"`    // Rate limit in milliseconds

	// SMTP specific fields
	Host     string `toml:"host"`     // SMTP host
	Port     int    `toml:"port"`     // SMTP port
	User     string `toml:"user"`     // SMTP username
	Password string `toml:"password"` // SMTP password
	TLS      bool   `toml:"tls"`      // Use TLS
	StartTLS bool   `toml:"startTLS"` // Use STARTTLS
	SSL      bool   `toml:"ssl"`      // Use SSL

	// SES specific fields
	Region          string `toml:"region"`          // AWS region
	AccessKeyId     string `toml:"accessKeyId"`     // AWS access key
	SecretAccessKey string `toml:"secretAccessKey"` // AWS secret key
	Endpoint        string `toml:"endpoint"`        // Custom endpoint (optional)

	// RMMail specific fields
	URL     string `toml:"url"`     // RMMail service URL
	APIKey  string `toml:"apiKey"`  // API key
	Timeout int    `toml:"timeout"` // Request timeout in seconds

	// SendGrid specific fields
	// APIKey is reused from above
	TemplateEngine bool `toml:"templateEngine"` // Use template engine

	// Sendmail specific fields
	Path string   `toml:"path"` // Path to sendmail binary
	Args []string `toml:"args"` // Command line arguments

	// Mock specific fields
	Verbose    bool   `toml:"verbose"`    // Verbose logging
	LogFile    string `toml:"logFile"`    // Log file path
	SaveToFile bool   `toml:"saveToFile"` // Save emails to file
}

// EmailEngineGlobalConfig represents global email engine configuration
type EmailEngineGlobalConfig struct {
	DefaultEngine       string `toml:"defaultEngine"`       // Default engine nameKey
	FallbackStrategy    string `toml:"fallbackStrategy"`    // Fallback strategy
	RetryCount          int    `toml:"retryCount"`          // Retry count
	RetryDelay          int    `toml:"retryDelay"`          // Retry delay in ms
	EnableFailover      bool   `toml:"enableFailover"`      // Enable failover
	HealthCheckInterval int    `toml:"healthCheckInterval"` // Health check interval in seconds
}

// FlexibleMailer manages multiple email engines with flexible configuration
type FlexibleMailer struct {
	engines      map[string]MailEngine         // nameKey -> engine instance
	configs      map[string]*EmailEngineConfig // nameKey -> config
	globalConfig *EmailEngineGlobalConfig      // Global configuration
	sortedKeys   []string                      // Sorted by priority
}

// LoadFlexibleConfig loads the flexible email engine configuration
func LoadFlexibleConfig() (*FlexibleMailer, error) {
	// Try to load new flexible configuration first
	var engineConfigs []EmailEngineConfig
	err := goconfig.UnmarshalKey("emailEngine", &engineConfigs)

	// If new config doesn't exist, try to load legacy config
	if err != nil || len(engineConfigs) == 0 {
		golog.Info("New emailEngine config not found, trying legacy config")
		legacyConfigs, legacyErr := loadLegacyConfig()
		if legacyErr != nil {
			return nil, fmt.Errorf("failed to load both new and legacy config. New: %v, Legacy: %v", err, legacyErr)
		}
		engineConfigs = legacyConfigs
	}

	// Load global configuration
	globalConfig := loadGlobalConfig()

	mailer := &FlexibleMailer{
		engines:      make(map[string]MailEngine),
		configs:      make(map[string]*EmailEngineConfig),
		globalConfig: globalConfig,
	}

	// Initialize engines
	for i := range engineConfigs {
		config := &engineConfigs[i]
		if !config.Enabled {
			golog.Info("Skipping disabled engine", "nameKey", config.NameKey)
			continue
		}

		engine, err := createEngineFromConfig(config)
		if err != nil {
			golog.Error("Failed to create engine", "nameKey", config.NameKey, "error", err)
			continue
		}

		mailer.engines[config.NameKey] = engine
		mailer.configs[config.NameKey] = config
		golog.Info("Initialized engine", "nameKey", config.NameKey, "type", config.Type)
	}

	// Sort engines by priority
	mailer.updateSortedKeys()

	return mailer, nil
}

// loadLegacyConfig loads configuration from the old mailEngine.* format
func loadLegacyConfig() ([]EmailEngineConfig, error) {
	var configs []EmailEngineConfig

	// Try to load Gmail config
	if gmailConfig := loadLegacySMTPConfig("gmail"); gmailConfig != nil {
		configs = append(configs, *gmailConfig)
	}

	// Try to load Outlook config
	if outlookConfig := loadLegacySMTPConfig("outlook"); outlookConfig != nil {
		configs = append(configs, *outlookConfig)
	}

	// Try to load QQ config
	if qqConfig := loadLegacySMTPConfig("qq"); qqConfig != nil {
		configs = append(configs, *qqConfig)
	}

	// Try to load custom config
	if customConfig := loadLegacySMTPConfig("custom"); customConfig != nil {
		configs = append(configs, *customConfig)
	}

	// Try to load SES config
	if sesConfig := loadLegacySESConfig(); sesConfig != nil {
		configs = append(configs, *sesConfig)
	}

	// Try to load RMMail config
	if rmConfig := loadLegacyRMMailConfig(); rmConfig != nil {
		configs = append(configs, *rmConfig)
	}

	if len(configs) == 0 {
		return nil, fmt.Errorf("no legacy configuration found")
	}

	golog.Info("Loaded legacy configuration", "engines", len(configs))
	return configs, nil
}

// loadLegacySMTPConfig loads SMTP config from legacy format
func loadLegacySMTPConfig(serviceName string) *EmailEngineConfig {
	configPath := fmt.Sprintf("mailEngine.%s", serviceName)

	// Check if config exists
	if goconfig.Config(configPath) == nil {
		return nil
	}

	// Get default email
	defaultEmail, _ := goconfig.ConfigString(fmt.Sprintf("%s.defaultEmail", configPath))

	// Get auth config
	authPath := fmt.Sprintf("%s.auth", configPath)
	if goconfig.Config(authPath) == nil {
		return nil
	}

	user, _ := goconfig.ConfigString(fmt.Sprintf("%s.user", authPath))
	pass, _ := goconfig.ConfigString(fmt.Sprintf("%s.pass", authPath))
	host, _ := goconfig.ConfigString(fmt.Sprintf("%s.host", authPath))
	port, _ := goconfig.ConfigInt(fmt.Sprintf("%s.port", authPath))

	if user == "" || pass == "" || host == "" || port == 0 {
		return nil
	}

	priority := 2 // Default priority
	if serviceName == "gmail" {
		priority = 1
	}

	return &EmailEngineConfig{
		NameKey:      serviceName,
		Type:         "smtp",
		Enabled:      true,
		DefaultEmail: defaultEmail,
		Priority:     priority,
		RateLimit:    250,
		Host:         host,
		Port:         port,
		User:         user,
		Password:     pass,
		TLS:          true,
		StartTLS:     true,
	}
}

// loadLegacySESConfig loads SES config from legacy format
func loadLegacySESConfig() *EmailEngineConfig {
	configPath := "mailEngine.ses"

	if goconfig.Config(configPath) == nil {
		return nil
	}

	defaultEmail, _ := goconfig.ConfigString(fmt.Sprintf("%s.defaultEmail", configPath))
	region, _ := goconfig.ConfigString(fmt.Sprintf("%s.region", configPath))
	accessKeyId, _ := goconfig.ConfigString(fmt.Sprintf("%s.accessKeyId", configPath))
	secretAccessKey, _ := goconfig.ConfigString(fmt.Sprintf("%s.secretAccessKey", configPath))

	if region == "" || accessKeyId == "" || secretAccessKey == "" {
		return nil
	}

	return &EmailEngineConfig{
		NameKey:         "ses-legacy",
		Type:            "ses",
		Enabled:         true,
		DefaultEmail:    defaultEmail,
		Priority:        2,
		RateLimit:       100,
		Region:          region,
		AccessKeyId:     accessKeyId,
		SecretAccessKey: secretAccessKey,
	}
}

// loadLegacyRMMailConfig loads RMMail config from legacy format
func loadLegacyRMMailConfig() *EmailEngineConfig {
	configPath := "mailEngine.rmMail"

	if goconfig.Config(configPath) == nil {
		return nil
	}

	defaultEmail, _ := goconfig.ConfigString(fmt.Sprintf("%s.defaultEmail", configPath))
	url, _ := goconfig.ConfigString(fmt.Sprintf("%s.url", configPath))

	if url == "" {
		return nil
	}

	return &EmailEngineConfig{
		NameKey:      "rmMail-legacy",
		Type:         "rmMail",
		Enabled:      true,
		DefaultEmail: defaultEmail,
		Priority:     3,
		RateLimit:    300,
		URL:          url,
	}
}

// loadGlobalConfig loads global configuration with defaults
func loadGlobalConfig() *EmailEngineGlobalConfig {
	config := &EmailEngineGlobalConfig{
		DefaultEngine:       "gmail",
		FallbackStrategy:    "priority",
		RetryCount:          3,
		RetryDelay:          1000,
		EnableFailover:      true,
		HealthCheckInterval: 300,
	}

	// Try to load from config if exists
	if defaultEngine, err := goconfig.ConfigString("emailEngineConfig.defaultEngine"); err == nil && defaultEngine != "" {
		config.DefaultEngine = defaultEngine
	}
	if strategy, err := goconfig.ConfigString("emailEngineConfig.fallbackStrategy"); err == nil && strategy != "" {
		config.FallbackStrategy = strategy
	}
	if retryCount, err := goconfig.ConfigInt("emailEngineConfig.retryCount"); err == nil {
		config.RetryCount = retryCount
	}
	if retryDelay, err := goconfig.ConfigInt("emailEngineConfig.retryDelay"); err == nil {
		config.RetryDelay = retryDelay
	}
	if enableFailover := goconfig.Config("emailEngineConfig.enableFailover"); enableFailover != nil {
		if val, ok := enableFailover.(bool); ok {
			config.EnableFailover = val
		}
	}
	if interval, err := goconfig.ConfigInt("emailEngineConfig.healthCheckInterval"); err == nil {
		config.HealthCheckInterval = interval
	}

	return config
}

// createEngineFromConfig creates an engine instance based on the configuration
func createEngineFromConfig(config *EmailEngineConfig) (MailEngine, error) {
	switch strings.ToLower(config.Type) {
	case "smtp":
		return createSMTPEngine(config)
	case "ses":
		return createSESEngine(config)
	case "rmmail":
		return createRMMailEngine(config)
	case "sendgrid":
		return createSendGridEngine(config)
	case "sendmail":
		return createSendmailEngine(config)
	case "mock":
		return createMockEngine(config)
	default:
		return nil, fmt.Errorf("unsupported engine type: %s", config.Type)
	}
}

// createSMTPEngine creates an SMTP engine from configuration
func createSMTPEngine(config *EmailEngineConfig) (MailEngine, error) {
	if config.Host == "" || config.Port == 0 {
		return nil, fmt.Errorf("SMTP host and port are required")
	}

	smtpConfig := &ServiceConfig{
		Service: config.NameKey,
		User:    config.User,
		Pass:    config.Password,
		Host:    config.Host,
		Port:    config.Port,
	}

	return NewGomailEngine(smtpConfig)
}

// createSESEngine creates an SES engine from configuration
func createSESEngine(config *EmailEngineConfig) (MailEngine, error) {
	if config.Region == "" || config.AccessKeyId == "" || config.SecretAccessKey == "" {
		return nil, fmt.Errorf("SES region, accessKeyId, and secretAccessKey are required")
	}

	sesConfig := map[string]interface{}{
		"region":          config.Region,
		"accessKeyId":     config.AccessKeyId,
		"secretAccessKey": config.SecretAccessKey,
		"defaultEmail":    config.DefaultEmail,
	}
	if config.Endpoint != "" {
		sesConfig["endpoint"] = config.Endpoint
	}

	engine := NewSESEngine(config.NameKey)
	if err := engine.Init(sesConfig); err != nil {
		return nil, err
	}
	return engine, nil
}

// createRMMailEngine creates an RMMail engine from configuration
func createRMMailEngine(config *EmailEngineConfig) (MailEngine, error) {
	if config.URL == "" {
		return nil, fmt.Errorf("RMMail URL is required")
	}

	rmConfig := map[string]interface{}{
		"url":          config.URL,
		"defaultEmail": config.DefaultEmail,
	}
	if config.APIKey != "" {
		rmConfig["apiKey"] = config.APIKey
	}
	if config.Timeout > 0 {
		rmConfig["timeout"] = config.Timeout
	}

	engine := NewRMMailEngine()
	if err := engine.Init(rmConfig); err != nil {
		return nil, err
	}
	return engine, nil
}

// createSendGridEngine creates a SendGrid engine from configuration
func createSendGridEngine(config *EmailEngineConfig) (MailEngine, error) {
	// This would need to be implemented based on your SendGrid engine
	return nil, fmt.Errorf("SendGrid engine not implemented yet")
}

// createSendmailEngine creates a Sendmail engine from configuration
func createSendmailEngine(config *EmailEngineConfig) (MailEngine, error) {
	path := config.Path
	if path == "" {
		path = DEFAULT_SENDMAIL_PATH
	}

	engine := NewSendmailEngine(path)
	sendmailConfig := map[string]interface{}{
		"defaultEmail": config.DefaultEmail,
	}
	if len(config.Args) > 0 {
		sendmailConfig["args"] = config.Args
	}

	if err := engine.Init(sendmailConfig); err != nil {
		return nil, err
	}
	return engine, nil
}

// createMockEngine creates a Mock engine from configuration
func createMockEngine(config *EmailEngineConfig) (MailEngine, error) {
	mockConfig := map[string]interface{}{
		"defaultEmail": config.DefaultEmail,
		"verbose":      0,
	}
	if config.Verbose {
		mockConfig["verbose"] = 1
	}
	if config.LogFile != "" {
		mockConfig["logFile"] = config.LogFile
	}
	if config.SaveToFile {
		mockConfig["saveToFile"] = config.SaveToFile
	}

	engine := NewMockMailEngine()
	if err := engine.Init(mockConfig); err != nil {
		return nil, err
	}
	return engine, nil
}

// updateSortedKeys updates the sorted keys based on priority
func (fm *FlexibleMailer) updateSortedKeys() {
	fm.sortedKeys = make([]string, 0, len(fm.configs))
	for nameKey := range fm.configs {
		fm.sortedKeys = append(fm.sortedKeys, nameKey)
	}

	sort.Slice(fm.sortedKeys, func(i, j int) bool {
		return fm.configs[fm.sortedKeys[i]].Priority < fm.configs[fm.sortedKeys[j]].Priority
	})
}

// SendMail sends an email using the specified engine nameKey
func (fm *FlexibleMailer) SendMail(nameKey string, msg *EmailMessage) error {
	engine, exists := fm.engines[nameKey]
	if !exists {
		return fmt.Errorf("engine not found: %s", nameKey)
	}

	config := fm.configs[nameKey]
	if msg.From == "" {
		msg.From = config.DefaultEmail
	}

	_, err := engine.Send(msg)
	return err
}

// SendMailWithFallback sends an email with automatic fallback
func (fm *FlexibleMailer) SendMailWithFallback(msg *EmailMessage) error {
	var lastErr error

	// Try engines in priority order
	for _, nameKey := range fm.sortedKeys {
		if err := fm.SendMail(nameKey, msg); err == nil {
			return nil
		} else {
			lastErr = err
			golog.Warn("Engine failed, trying next", "nameKey", nameKey, "error", err)
		}
	}

	return fmt.Errorf("all engines failed, last error: %v", lastErr)
}

// GetAvailableEngines returns a list of available engine nameKeys
func (fm *FlexibleMailer) GetAvailableEngines() []string {
	engines := make([]string, 0, len(fm.engines))
	for nameKey := range fm.engines {
		engines = append(engines, nameKey)
	}
	return engines
}

// GetEngineConfig returns the configuration for a specific engine
func (fm *FlexibleMailer) GetEngineConfig(nameKey string) *EmailEngineConfig {
	return fm.configs[nameKey]
}
