# go-mail

A flexible and feature-rich email service package for Go applications.

## Features

- Multiple email engine support:
  - Gmail SMTP
  - Amazon SES
  - Sendmail
  - RM Mail
  - Mock Mail (for testing)
- HTML and plain text email support
- Email validation
- Parameter replacement in email templates
- HTML to text conversion
- Email logging and tracking
- Priority email support
- Custom headers support
- Reply-to address support

## Installation

```bash
go mod init github.com/real-rm/gomail
go get github.com/real-rm/golog
```

## Quick Start

```go
package main

import (
    "github.com/real-rm/gomail"
)

func main() {
    // Get mailer instance
    mailer, err := gomail.GetSendMailObj()
    if err != nil {
        panic(err)
    }

    // Create email message
    msg := &gomail.EmailMessage{
        From:    "<EMAIL>",
        To:      []string{"<EMAIL>"},
        Subject: "Test Email",
        Text:    "This is a test email",
        HTML:    "<p>This is a test email</p>",
    }

    // Send email using Gmail engine
    err = mailer.SendMail("gmail", msg)
    if err != nil {
        panic(err)
    }
}
```

## Configuration

The package supports multiple email engines that can be configured through your application's configuration:

- **SMTP Services** (using universal GomailEngine):
  - Gmail SMTP
  - Outlook/Hotmail SMTP
  - QQ Mail SMTP
  - Custom SMTP servers
- **Cloud Services**:
  - Amazon SES
- **System Services**:
  - Sendmail
  - RM Mail
- **Testing**:
  - Mock Mail (for testing)

Each engine can be configured with its own settings like SMTP credentials, rate limits, and default sender addresses.

## Multi-SMTP Support

The package now supports multiple SMTP services simultaneously. You can configure different SMTP providers and switch between them as needed:

```go
// Send email using Gmail
err := mailer.SendMail("gmail", msg)

// Send email using Outlook
err := mailer.SendMail("outlook", msg)

// Send email using QQ Mail
err := mailer.SendMail("qq", msg)

// Send email using custom SMTP
err := mailer.SendMail("custom", msg)
```

### Configuration Example

```ini
# Gmail SMTP
[mailEngine.gmail]
defaultEmail = "<EMAIL>"
[mailEngine.gmail.auth]
user = "<EMAIL>"
pass = "your-app-password"
host = "smtp.gmail.com"
port = 587

# Outlook SMTP
[mailEngine.outlook]
defaultEmail = "<EMAIL>"
[mailEngine.outlook.auth]
user = "<EMAIL>"
pass = "your-password"
host = "smtp-mail.outlook.com"
port = 587

# Custom SMTP
[mailEngine.custom]
defaultEmail = "<EMAIL>"
[mailEngine.custom.auth]
user = "<EMAIL>"
pass = "your-password"
host = "smtp.yourcompany.com"
port = 587
```

## API Documentation

For detailed API documentation, please refer to the [api.md](api.md) file.

## Dependencies

- github.com/real-rm/golog - For logging
- github.com/real-rm/goconfig - For configuration management
- github.com/jaytaylor/html2text - For HTML to text conversion
- go.mongodb.org/mongo-driver - For email logging (optional)

## License

This project is licensed under the MIT License - see the LICENSE file for details. 
