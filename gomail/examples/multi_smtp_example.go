package main

import (
	"fmt"
	"log"

	gomail "github.com/real-rm/gomail"
)

func main() {
	// Initialize the mailer
	mailer, err := gomail.GetSendMailObj()
	if err != nil {
		log.Fatalf("Failed to initialize mailer: %v", err)
	}

	// Example 1: Send email using Gmail
	err = sendEmailWithGmail(mailer)
	if err != nil {
		log.Printf("Failed to send email with Gmail: %v", err)
	}

	// Example 2: Send email using Outlook
	err = sendEmailWithOutlook(mailer)
	if err != nil {
		log.Printf("Failed to send email with Outlook: %v", err)
	}

	// Example 3: Send email using QQ Mail
	err = sendEmailWithQQ(mailer)
	if err != nil {
		log.Printf("Failed to send email with QQ: %v", err)
	}

	// Example 4: Send email using Custom SMTP
	err = sendEmailWithCustomSMTP(mailer)
	if err != nil {
		log.Printf("Failed to send email with Custom SMTP: %v", err)
	}

	// Example 5: Send email directly using SMTP engine
	err = sendEmailDirectly()
	if err != nil {
		log.Printf("Failed to send email directly: %v", err)
	}
}

func sendEmailWithGmail(mailer *gomail.Mailer) error {
	msg := &gomail.EmailMessage{
		From:    "<EMAIL>",
		To:      []string{"<EMAIL>"},
		Subject: "Test Email from Gmail",
		Text:    "This is a test email sent using Gmail SMTP.",
		HTML:    "<p>This is a <strong>test email</strong> sent using Gmail SMTP.</p>",
		Headers: map[string]string{
			"X-Mailer": "GoMail Multi-SMTP",
		},
	}

	err := mailer.SendMail("gmail", msg)
	if err != nil {
		return fmt.Errorf("failed to send email with Gmail: %v", err)
	}

	fmt.Println("Email sent successfully using Gmail!")
	return nil
}

func sendEmailWithOutlook(mailer *gomail.Mailer) error {
	msg := &gomail.EmailMessage{
		From:    "<EMAIL>",
		To:      []string{"<EMAIL>"},
		Subject: "Test Email from Outlook",
		Text:    "This is a test email sent using Outlook SMTP.",
		HTML:    "<p>This is a <strong>test email</strong> sent using Outlook SMTP.</p>",
		Headers: map[string]string{
			"X-Mailer": "GoMail Multi-SMTP",
		},
	}

	err := mailer.SendMail("outlook", msg)
	if err != nil {
		return fmt.Errorf("failed to send email with Outlook: %v", err)
	}

	fmt.Println("Email sent successfully using Outlook!")
	return nil
}

func sendEmailWithQQ(mailer *gomail.Mailer) error {
	msg := &gomail.EmailMessage{
		From:    "<EMAIL>",
		To:      []string{"<EMAIL>"},
		Subject: "Test Email from QQ Mail",
		Text:    "This is a test email sent using QQ Mail SMTP.",
		HTML:    "<p>This is a <strong>test email</strong> sent using QQ Mail SMTP.</p>",
		Headers: map[string]string{
			"X-Mailer": "GoMail Multi-SMTP",
		},
	}

	err := mailer.SendMail("qq", msg)
	if err != nil {
		return fmt.Errorf("failed to send email with QQ: %v", err)
	}

	fmt.Println("Email sent successfully using QQ Mail!")
	return nil
}

func sendEmailWithCustomSMTP(mailer *gomail.Mailer) error {
	msg := &gomail.EmailMessage{
		From:    "<EMAIL>",
		To:      []string{"<EMAIL>"},
		Subject: "Test Email from Custom SMTP",
		Text:    "This is a test email sent using Custom SMTP server.",
		HTML:    "<p>This is a <strong>test email</strong> sent using Custom SMTP server.</p>",
		Headers: map[string]string{
			"X-Mailer": "GoMail Multi-SMTP",
		},
	}

	err := mailer.SendMail("custom", msg)
	if err != nil {
		return fmt.Errorf("failed to send email with Custom SMTP: %v", err)
	}

	fmt.Println("Email sent successfully using Custom SMTP!")
	return nil
}

// sendEmailDirectly demonstrates how to create and use SMTP engines directly
func sendEmailDirectly() error {
	// Create SMTP configuration for a custom service
	smtpConfig := &gomail.ServiceConfig{
		Service: "Custom",
		User:    "<EMAIL>",
		Pass:    "your-password",
		Host:    "smtp.yourcompany.com",
		Port:    587,
	}

	// Create GomailEngine directly
	engine, err := gomail.NewGomailEngine(smtpConfig)
	if err != nil {
		return fmt.Errorf("failed to create SMTP engine: %v", err)
	}

	// Create email message
	msg := &gomail.EmailMessage{
		From:    "<EMAIL>",
		To:      []string{"<EMAIL>"},
		Subject: "Direct SMTP Test",
		Text:    "This email was sent using a directly created SMTP engine.",
		HTML:    "<p>This email was sent using a <strong>directly created</strong> SMTP engine.</p>",
	}

	// Send email
	_, err = engine.Send(msg)
	if err != nil {
		return fmt.Errorf("failed to send email: %v", err)
	}

	fmt.Println("Email sent successfully using direct SMTP engine!")
	return nil
}
