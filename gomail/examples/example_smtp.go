// This is an example program - rename to main package when running
// go run example_smtp.go
package gomail_example

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	gohelper "github.com/real-rm/gohelper"
	"github.com/real-rm/gomail"
)

func main() {
	// Setup configuration
	currentDir, err := os.Getwd()
	if err != nil {
		log.Fatalf("Failed to get current directory: %v", err)
	}

	configPath, err := filepath.Abs(filepath.Join(currentDir, "smtp.test.ini"))
	if err != nil {
		log.Fatalf("Failed to get absolute path: %v", err)
	}

	if _, err := os.Stat(configPath); err != nil {
		log.Fatalf("Config file not found at %s: %v", configPath, err)
	}

	gohelper.SetRmbaseFileCfg(configPath)

	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		log.Fatalf("Failed to setup test environment: %v", err)
	}
	defer gohelper.CleanupTestEnv()

	// --- SMTP Server Configuration ---
	smtpHost := "cac1.realmaster.me" // e.g., "mail.example.com"
	smtpPort := 587                  // e.g., 465 for SMTPS, 587 for STARTTLS

	// --- Sender Credentials ---
	// Replace with your email address and password from your Stalwart server.
	senderName := "Real Master Info"
	senderEmail := "<EMAIL>"  // e.g., "<EMAIL>"
	senderPassword := "3GQi18O5H80zbjh3" // IMPORTANT: In a real application, do not hardcode passwords.
	// Use environment variables or a secure configuration management system.

	// --- Recipient and Email Content ---
	recipientName := "Fred"
	recipientEmailAddress := "<EMAIL>" // e.g.

	fmt.Println("=== GoMail SMTP Example ===")
	fmt.Printf("SMTP Server: %s:%d\n", smtpHost, smtpPort)
	fmt.Printf("Sender: %s <%s>\n", senderName, senderEmail)
	fmt.Printf("Recipient: %s <%s>\n", recipientName, recipientEmailAddress)
	fmt.Println()

	// Method 1: Direct SMTP Engine Usage
	fmt.Println("Method 1: Using SMTP Engine Directly")
	err = sendEmailDirectly(smtpHost, smtpPort, senderEmail, senderPassword, senderName, recipientName, recipientEmailAddress)
	if err != nil {
		log.Printf("Failed to send email directly: %v", err)
	} else {
		fmt.Println("✓ Email sent successfully using direct SMTP engine")
	}
	fmt.Println()

	// Method 2: Using Mailer (recommended approach from README)
	fmt.Println("Method 2: Using Mailer (README approach)")
	err = sendEmailWithMailer(senderEmail, senderName, recipientName, recipientEmailAddress)
	if err != nil {
		log.Printf("Failed to send email with mailer: %v", err)
	} else {
		fmt.Println("✓ Email sent successfully using Mailer")
	}
	fmt.Println()

	fmt.Println("=== Email sending completed ===")
}

// sendEmailDirectly demonstrates sending email using SMTP engine directly
func sendEmailDirectly(smtpHost string, smtpPort int, senderEmail, senderPassword, senderName, recipientName, recipientEmailAddress string) error {
	// Create SMTP configuration
	smtpConfig := &gomail.ServiceConfig{
		Service: "SMTP",
		User:    senderEmail,
		Pass:    senderPassword,
		Host:    smtpHost,
		Port:    smtpPort,
	}

	// Create GomailEngine with SMTP config
	engine, err := gomail.NewGomailEngine(smtpConfig)
	if err != nil {
		return fmt.Errorf("failed to create SMTP engine: %v", err)
	}

	// Create email message
	msg := &gomail.EmailMessage{
		From:    senderEmail,
		To:      []string{recipientEmailAddress},
		Subject: "Test Email from Stalwart SMTP Server (Direct)",
		Text: fmt.Sprintf(`Hello %s,

This is a test email sent directly from the Stalwart SMTP server using the gomail package.

This email was sent using the direct SMTP engine approach.

Best regards,
%s`, recipientName, senderName),
		HTML: fmt.Sprintf(`<html>
<body>
    <h2>Hello %s,</h2>
    <p>This is a test email sent directly from the <strong>Stalwart SMTP server</strong> using the gomail package.</p>
    <p>This email was sent using the <em>direct SMTP engine</em> approach.</p>
    <p>Best regards,<br/>%s</p>
</body>
</html>`, recipientName, senderName),
		ReplyTo: senderEmail,
		Headers: map[string]string{
			"X-Mailer":    "GoMail SMTP Direct",
			"X-Test-Type": "Direct SMTP Engine",
		},
	}

	// Send the email
	result, err := engine.Send(msg)
	if err != nil {
		return fmt.Errorf("failed to send email: %v", err)
	}

	fmt.Printf("  Result: %s\n", result)
	return nil
}

// sendEmailWithMailer demonstrates the recommended approach from README
func sendEmailWithMailer(senderEmail, senderName, recipientName, recipientEmailAddress string) error {
	// Get mailer instance (as shown in README)
	mailer, err := gomail.GetSendMailObj()
	if err != nil {
		return fmt.Errorf("failed to get mailer: %v", err)
	}

	// Create email message (as shown in README)
	msg := &gomail.EmailMessage{
		From:    senderEmail,
		To:      []string{recipientEmailAddress},
		Subject: "Test Email from Stalwart SMTP Server (Mailer)",
		Text: fmt.Sprintf(`Hello %s,

This is a test email sent from the Stalwart SMTP server using the gomail package.

This email was sent using the recommended Mailer approach from the README.

Best regards,
%s`, recipientName, senderName),
		HTML: fmt.Sprintf(`<html>
<body>
    <h2>Hello %s,</h2>
    <p>This is a test email sent from the <strong>Stalwart SMTP server</strong> using the gomail package.</p>
    <p>This email was sent using the <em>recommended Mailer approach</em> from the README.</p>
    <p>Best regards,<br/>%s</p>
</body>
</html>`, recipientName, senderName),
		ReplyTo: senderEmail,
		Headers: map[string]string{
			"X-Mailer":    "GoMail Mailer",
			"X-Test-Type": "Mailer Approach",
		},
	}

	// Send email using Gmail engine (as shown in README)
	// Note: The "gmail" engine will use the SMTP configuration from the config file
	err = mailer.SendMail("gmail", msg)
	if err != nil {
		return fmt.Errorf("failed to send email: %v", err)
	}

	fmt.Printf("  Email sent via Mailer using 'gmail' engine\n")
	return nil
}
