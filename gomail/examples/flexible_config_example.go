package main

import (
	"fmt"
	"log"

	gomail "github.com/real-rm/gomail"
)

func main() {
	// Load flexible configuration
	mailer, err := gomail.LoadFlexibleConfig()
	if err != nil {
		log.Fatalf("Failed to load flexible config: %v", err)
	}

	// Show available engines
	fmt.Println("Available engines:")
	for _, nameKey := range mailer.GetAvailableEngines() {
		config := mailer.GetEngineConfig(nameKey)
		fmt.Printf("  - %s (type: %s, priority: %d)\n", nameKey, config.Type, config.Priority)
	}

	// Example 1: Send email using specific engine by nameKey
	err = sendEmailWithSpecificEngine(mailer)
	if err != nil {
		log.Printf("Failed to send email with specific engine: %v", err)
	}

	// Example 2: Send email with automatic fallback
	err = sendEmailWithFallback(mailer)
	if err != nil {
		log.Printf("Failed to send email with fallback: %v", err)
	}

	// Example 3: Send different types of emails using different engines
	err = sendDifferentEmailTypes(mailer)
	if err != nil {
		log.Printf("Failed to send different email types: %v", err)
	}
}

func sendEmailWithSpecificEngine(mailer *gomail.FlexibleMailer) error {
	msg := &gomail.EmailMessage{
		To:      []string{"<EMAIL>"},
		Subject: "Test Email from Gmail Primary",
		Text:    "This email was sent using the gmail-primary engine.",
		HTML:    "<p>This email was sent using the <strong>gmail-primary</strong> engine.</p>",
		Headers: map[string]string{
			"X-Mailer":    "GoMail Flexible Config",
			"X-Engine":    "gmail-primary",
		},
	}

	// Send using specific engine by nameKey
	err := mailer.SendMail("gmail-primary", msg)
	if err != nil {
		return fmt.Errorf("failed to send with gmail-primary: %v", err)
	}

	fmt.Println("Email sent successfully using gmail-primary!")
	return nil
}

func sendEmailWithFallback(mailer *gomail.FlexibleMailer) error {
	msg := &gomail.EmailMessage{
		To:      []string{"<EMAIL>"},
		Subject: "Test Email with Automatic Fallback",
		Text:    "This email was sent using automatic fallback mechanism.",
		HTML:    "<p>This email was sent using <strong>automatic fallback</strong> mechanism.</p>",
		Headers: map[string]string{
			"X-Mailer":    "GoMail Flexible Config",
			"X-Strategy":  "fallback",
		},
	}

	// Send with automatic fallback (tries engines in priority order)
	err := mailer.SendMailWithFallback(msg)
	if err != nil {
		return fmt.Errorf("failed to send with fallback: %v", err)
	}

	fmt.Println("Email sent successfully using fallback mechanism!")
	return nil
}

func sendDifferentEmailTypes(mailer *gomail.FlexibleMailer) error {
	// Marketing email using high-priority engine
	marketingMsg := &gomail.EmailMessage{
		To:      []string{"<EMAIL>"},
		Subject: "Special Offer - Don't Miss Out!",
		Text:    "Check out our special offer...",
		HTML:    "<h1>Special Offer</h1><p>Check out our special offer...</p>",
		Headers: map[string]string{
			"X-Email-Type": "marketing",
		},
	}

	err := mailer.SendMail("gmail-primary", marketingMsg)
	if err != nil {
		return fmt.Errorf("failed to send marketing email: %v", err)
	}
	fmt.Println("Marketing email sent using gmail-primary!")

	// Transactional email using reliable engine
	transactionalMsg := &gomail.EmailMessage{
		To:      []string{"<EMAIL>"},
		Subject: "Your Order Confirmation #12345",
		Text:    "Thank you for your order. Your order number is #12345...",
		HTML:    "<h2>Order Confirmation</h2><p>Thank you for your order. Your order number is <strong>#12345</strong>...</p>",
		Headers: map[string]string{
			"X-Email-Type": "transactional",
		},
	}

	err = mailer.SendMail("aws-ses-primary", transactionalMsg)
	if err != nil {
		return fmt.Errorf("failed to send transactional email: %v", err)
	}
	fmt.Println("Transactional email sent using aws-ses-primary!")

	// System notification using backup engine
	systemMsg := &gomail.EmailMessage{
		To:      []string{"<EMAIL>"},
		Subject: "System Alert: High CPU Usage",
		Text:    "Warning: CPU usage is above 90% on server-01...",
		HTML:    "<h3>System Alert</h3><p><strong>Warning:</strong> CPU usage is above 90% on server-01...</p>",
		Headers: map[string]string{
			"X-Email-Type": "system",
			"X-Priority":   "high",
		},
	}

	err = mailer.SendMail("company-smtp", systemMsg)
	if err != nil {
		return fmt.Errorf("failed to send system email: %v", err)
	}
	fmt.Println("System email sent using company-smtp!")

	return nil
}

// Example of how to use different engines for different purposes
func demonstrateEngineSelection(mailer *gomail.FlexibleMailer) {
	fmt.Println("\n=== Engine Selection Examples ===")

	// Get all available engines
	engines := mailer.GetAvailableEngines()
	
	for _, nameKey := range engines {
		config := mailer.GetEngineConfig(nameKey)
		
		fmt.Printf("\nEngine: %s\n", nameKey)
		fmt.Printf("  Type: %s\n", config.Type)
		fmt.Printf("  Priority: %d\n", config.Priority)
		fmt.Printf("  Default Email: %s\n", config.DefaultEmail)
		fmt.Printf("  Rate Limit: %dms\n", config.RateLimit)
		
		// Show type-specific information
		switch config.Type {
		case "smtp":
			fmt.Printf("  SMTP Host: %s:%d\n", config.Host, config.Port)
			fmt.Printf("  TLS: %t, StartTLS: %t\n", config.TLS, config.StartTLS)
		case "ses":
			fmt.Printf("  AWS Region: %s\n", config.Region)
		case "rmMail":
			fmt.Printf("  Service URL: %s\n", config.URL)
		}
		
		// Suggest use cases
		switch {
		case config.Priority == 1:
			fmt.Printf("  Recommended for: Critical/Transactional emails\n")
		case config.Priority <= 3:
			fmt.Printf("  Recommended for: Regular/Marketing emails\n")
		default:
			fmt.Printf("  Recommended for: Backup/Testing\n")
		}
	}
}
