package goresodownload

import (
	"bytes"
	"context"
	"fmt"
	"image"
	"image/color"
	"image/jpeg"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/real-rm/gospeedmeter"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	gohelper "github.com/real-rm/gohelper"
	gomongo "github.com/real-rm/gomongo"
)

// createTestImage creates a small test image and returns it as JPEG bytes
func createTestImage() ([]byte, error) {
	// Create a 10x10 image
	img := image.NewRGBA(image.Rect(0, 0, 10, 10))

	// Fill with a simple pattern
	for y := 0; y < 10; y++ {
		for x := 0; x < 10; x++ {
			img.Set(x, y, color.RGBA{uint8(x * 25), uint8(y * 25), 128, 255})
		}
	}

	// Encode as JPEG
	var buf bytes.Buffer
	if err := jpeg.Encode(&buf, img, &jpeg.Options{Quality: 90}); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

// setupDownloaderTest sets up the test environment for downloader tests
func setupDownloaderTest(t *testing.T) (*gomongo.MongoCollection, *gomongo.MongoCollection, func()) {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}
	gohelper.SetRmbaseFileCfg(configPath)

	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}

	// Initialize MongoDB
	if err := gomongo.InitMongoDB(); err != nil {
		t.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Create test collections
	mergedColl := gomongo.Coll("rni", "merged")
	failedColl := gomongo.Coll("rni", "failed")
	if mergedColl == nil || failedColl == nil {
		t.Fatalf("Failed to get test collections")
	}

	// Clean up collections before test
	if _, err := mergedColl.DeleteMany(context.Background(), bson.M{}); err != nil {
		t.Errorf("Failed to cleanup merged collection: %v", err)
	}
	if _, err := failedColl.DeleteMany(context.Background(), bson.M{}); err != nil {
		t.Errorf("Failed to cleanup failed collection: %v", err)
	}

	// Create test directories
	testDir := filepath.Join(currentDir, "test_data")
	if err := os.MkdirAll(testDir, 0755); err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}

	// Return collections and cleanup function
	return mergedColl, failedColl, func() {
		// Clean up collections after test
		if _, err := mergedColl.DeleteMany(context.Background(), bson.M{}); err != nil {
			t.Errorf("Failed to cleanup merged collection: %v", err)
		}
		if _, err := failedColl.DeleteMany(context.Background(), bson.M{}); err != nil {
			t.Errorf("Failed to cleanup failed collection: %v", err)
		}
		// Clean up test directory
		if err := os.RemoveAll(testDir); err != nil {
			t.Errorf("Failed to cleanup test directory: %v", err)
		}
	}
}

func TestNewDownloader(t *testing.T) {
	mergedColl, failedColl, cleanup := setupDownloaderTest(t)
	defer cleanup()

	tests := []struct {
		name    string
		opts    *DownloaderOptions
		wantErr bool
	}{
		{
			name: "valid options",
			opts: &DownloaderOptions{
				Config:       NewDefaultConfig(),
				StoragePaths: []string{"/tmp/test"},
				MergedCol:    mergedColl,
				FailedCol:    failedColl,
			},
			wantErr: false,
		},
		{
			name:    "nil options",
			opts:    nil,
			wantErr: false,
		},
		{
			name: "nil config",
			opts: &DownloaderOptions{
				StoragePaths: []string{"/tmp/test"},
				MergedCol:    mergedColl,
				FailedCol:    failedColl,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewDownloader(tt.opts)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				if tt.opts == nil {
					assert.NotNil(t, got.Config)
				} else if tt.opts.Config == nil {
					assert.NotNil(t, got.Config)
				}
			}
		})
	}
}

func TestDownloader_ProcessAnalysisResult(t *testing.T) {
	// Setup test environment
	mergedColl, failedColl, cleanup := setupDownloaderTest(t)
	defer cleanup()

	// Create test directories
	testDirs := []string{"dir1", "dir2"}
	for _, dir := range testDirs {
		dirPath := filepath.Join("test_data", dir)
		// Remove directory if it exists
		if err := os.RemoveAll(dirPath); err != nil {
			t.Fatalf("Failed to cleanup directory %s: %v", dirPath, err)
		}
		// Create fresh directory
		if err := os.MkdirAll(dirPath, 0755); err != nil {
			t.Fatalf("Failed to create test directory %s: %v", dirPath, err)
		}
	}

	// Create test image
	testImage, err := createTestImage()
	if err != nil {
		t.Fatalf("Failed to create test image: %v", err)
	}

	// Create mock server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "image/jpeg")
		if _, err := w.Write(testImage); err != nil {
			t.Fatalf("Failed to write test image: %v", err)
		}
	}))
	defer server.Close()

	// Test cases
	testCases := []struct {
		name          string
		result        *AnalysisResult
		board         string
		expectedError bool
		setup         func(docID string) error
		verify        func(t *testing.T, docID string)
	}{
		{
			name: "successful_download_and_update",
			result: &AnalysisResult{
				ID:     primitive.NewObjectID().Hex(),
				Sid:    "test123",
				PropTs: time.Now(),
				DownloadTasks: []MediaTask{
					{
						Sid:      "test123",
						MediaKey: "key1",
						URL:      server.URL + "/test.jpg",
						Type:     "image/jpeg",
						DestPath: "images/test.jpg",
						IsPhoto:  true,
					},
				},
				NewFirstPic: MediaTask{
					Sid:      "test123",
					MediaKey: "key1",
					URL:      server.URL + "/test.jpg",
					Type:     "image/jpeg",
					DestPath: "images/test.jpg",
					IsPhoto:  true,
				},
				PhoLH: []int32{123, 456},
				DocLH: []string{"789.pdf"},
			},
			board:         "TRB",
			expectedError: false,
			setup: func(docID string) error {
				// Insert initial document
				_, err := mergedColl.InsertOne(context.Background(), bson.M{
					"_id":   docID,
					"phoLH": []int32{},
					"docLH": []string{},
				})
				return err
			},
			verify: func(t *testing.T, docID string) {
				// Verify document was updated with correct fields
				var doc bson.M
				err := mergedColl.FindOne(context.Background(), bson.M{"_id": docID}).Decode(&doc)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				// Check PhoLH
				phoLH, ok := doc["phoLH"].(bson.A)
				if !ok {
					t.Error("phoLH field not found or wrong type")
				} else {
					expectedPhoLH := []int{123, 456}
					actualPhoLH := make([]int, len(phoLH))
					for i, v := range phoLH {
						actualPhoLH[i] = int(v.(int32))
					}
					if !reflect.DeepEqual(actualPhoLH, expectedPhoLH) {
						t.Errorf("Expected PhoLH %v, got %v", expectedPhoLH, actualPhoLH)
					}
				}

				// Check DocLH
				docLH, ok := doc["docLH"].(bson.A)
				if !ok {
					t.Error("docLH field not found or wrong type")
				} else {
					expectedDocLH := []string{"789.pdf"}
					actualDocLH := make([]string, len(docLH))
					for i, v := range docLH {
						actualDocLH[i] = v.(string)
					}
					if !reflect.DeepEqual(actualDocLH, expectedDocLH) {
						t.Errorf("Expected DocLH %v, got %v", expectedDocLH, actualDocLH)
					}
				}

				// Check tnLH (should be present and non-zero)
				tnLH, ok := doc["tnLH"].(int32)
				if !ok {
					t.Error("tnLH field not found or wrong type")
				} else if tnLH == 0 {
					t.Error("tnLH should not be zero")
				}
			},
		},
		{
			name: "successful_deletion",
			result: &AnalysisResult{
				ID:     primitive.NewObjectID().Hex(),
				Sid:    "test456",
				PropTs: time.Now(),
				DeleteTasks: []DeleteTask{
					{
						Sid:      "test123",
						MediaKey: "key1",
						Path:     "images/test.jpg",
					},
				},
				PhoLH: []int32{123},
				DocLH: []string{"456.pdf"},
			},
			board:         "CAR",
			expectedError: false,
			setup: func(docID string) error {
				// Create test file in each directory
				for _, dir := range testDirs {
					dirPath := filepath.Join("test_data", dir, "images")
					// Remove directory if it exists
					if err := os.RemoveAll(dirPath); err != nil {
						return fmt.Errorf("failed to cleanup images directory: %w", err)
					}
					// Create fresh directory
					if err := os.MkdirAll(dirPath, 0755); err != nil {
						return fmt.Errorf("failed to create images directory: %w", err)
					}
					filePath := filepath.Join(dirPath, "test.jpg")
					if err := os.WriteFile(filePath, testImage, 0644); err != nil {
						return fmt.Errorf("failed to create test file: %w", err)
					}
				}

				// Insert initial document
				_, err := mergedColl.InsertOne(context.Background(), bson.M{
					"_id":   docID,
					"phoLH": []int32{},
					"docLH": []string{},
				})
				return err
			},
			verify: func(t *testing.T, docID string) {
				// Verify document was updated with correct fields
				var doc bson.M
				err := mergedColl.FindOne(context.Background(), bson.M{"_id": docID}).Decode(&doc)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				// Check PhoLH
				phoLH, ok := doc["phoLH"].(bson.A)
				if !ok {
					t.Error("phoLH field not found or wrong type")
				} else {
					expectedPhoLH := []int{123}
					actualPhoLH := make([]int, len(phoLH))
					for i, v := range phoLH {
						actualPhoLH[i] = int(v.(int32))
					}
					if !reflect.DeepEqual(actualPhoLH, expectedPhoLH) {
						t.Errorf("Expected PhoLH %v, got %v", expectedPhoLH, actualPhoLH)
					}
				}

				// Check DocLH
				docLH, ok := doc["docLH"].(bson.A)
				if !ok {
					t.Error("docLH field not found or wrong type")
				} else {
					expectedDocLH := []string{"456.pdf"}
					actualDocLH := make([]string, len(docLH))
					for i, v := range docLH {
						actualDocLH[i] = v.(string)
					}
					if !reflect.DeepEqual(actualDocLH, expectedDocLH) {
						t.Errorf("Expected DocLH %v, got %v", expectedDocLH, actualDocLH)
					}
				}

				// Check tnLH (should not be present for deletion)
				if _, ok := doc["tnLH"]; ok {
					t.Error("tnLH should not be present for deletion")
				}
			},
		},
	}

	// Run test cases
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Run setup if provided
			if tc.setup != nil {
				if err := tc.setup(tc.result.ID); err != nil {
					t.Fatalf("Setup failed: %v", err)
				}
			}

			// Create downloader instance
			downloader, err := NewDownloader(&DownloaderOptions{
				Config:       NewDefaultConfig(),
				StoragePaths: []string{filepath.Join("test_data", "dir1"), filepath.Join("test_data", "dir2")},
				MergedCol:    mergedColl,
				FailedCol:    failedColl,
			})
			if err != nil {
				t.Fatalf("Failed to create downloader: %v", err)
			}

			// Process analysis result
			_, err = downloader.ProcessAnalysisResult(tc.result, tc.board)
			if tc.expectedError {
				if err == nil {
					t.Error("Expected error but got nil")
				}
			} else if err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			// Run verification if provided
			if tc.verify != nil {
				tc.verify(t, tc.result.ID)
			}
		})
	}
}

func TestDownloader_ProcessAnalysisResult_ErrorCases(t *testing.T) {
	mergedColl, failedColl, cleanup := setupDownloaderTest(t)
	defer cleanup()

	downloader, err := NewDownloader(&DownloaderOptions{
		Config:       NewDefaultConfig(),
		StoragePaths: []string{"/tmp"},
		MergedCol:    mergedColl,
		FailedCol:    failedColl,
	})
	assert.NoError(t, err)

	testCases := []struct {
		name    string
		result  *AnalysisResult
		board   string
		setup   func(docID string) error
		wantErr bool
	}{
		{
			name: "invalid_url",
			result: &AnalysisResult{
				ID:     primitive.NewObjectID().Hex(),
				Sid:    "test123",
				PropTs: time.Now(),
				DownloadTasks: []MediaTask{{
					Sid:      "test",
					MediaKey: "key",
					URL:      "http://invalid-url",
					Type:     "image/jpeg",
					DestPath: "images/test.jpg",
					IsPhoto:  true,
				}},
			},
			board: "TRB",
			setup: func(docID string) error {
				_, err := mergedColl.InsertOne(context.Background(), bson.M{"_id": docID, "phoLH": []int32{}, "docLH": []string{}})
				return err
			},
			wantErr: true,
		},
		{
			name: "invalid_dest_path",
			result: &AnalysisResult{
				ID:     primitive.NewObjectID().Hex(),
				Sid:    "test456",
				PropTs: time.Now(),
				DownloadTasks: []MediaTask{{
					Sid:      "test",
					MediaKey: "key",
					URL:      "http://example.com/test.jpg",
					Type:     "image/jpeg",
					DestPath: "/invalid_path/test.jpg",
					IsPhoto:  true,
				}},
			},
			board: "CAR",
			setup: func(docID string) error {
				_, err := mergedColl.InsertOne(context.Background(), bson.M{"_id": docID, "phoLH": []int32{}, "docLH": []string{}})
				return err
			},
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if tc.setup != nil {
				_ = tc.setup(tc.result.ID)
			}
			_, err := downloader.ProcessAnalysisResult(tc.result, tc.board)
			if tc.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestGracefulShutdown tests that the downloader gracefully shuts down
// without leaving hanging goroutines or incomplete tasks
func TestGracefulShutdown(t *testing.T) {
	// Create a downloader with minimal configuration
	config := NewDefaultConfig()
	config.PropConcurrency = 2

	downloader, err := NewDownloader(&DownloaderOptions{
		Config:       config,
		StoragePaths: []string{"/tmp/test_downloads"},
	})
	if err != nil {
		t.Fatalf("Failed to create downloader: %v", err)
	}

	// Create some test tasks
	tasks := []MediaTask{
		{
			URL:      "https://httpbin.org/delay/2", // 2 second delay
			DestPath: "test1.jpg",
			IsPhoto:  true,
		},
		{
			URL:      "https://httpbin.org/delay/3", // 3 second delay
			DestPath: "test2.jpg",
			IsPhoto:  true,
		},
	}

	// Start processing tasks in a goroutine
	var wg sync.WaitGroup
	var processingErr error

	wg.Add(1)
	go func() {
		defer wg.Done()

		// Simulate processing analysis result
		result := &AnalysisResult{
			ID:            "test-prop-123",
			DownloadTasks: tasks,
			DeleteTasks:   []DeleteTask{},
		}

		_, err := downloader.ProcessAnalysisResult(result, "TEST")
		processingErr = err
	}()

	// Wait a bit to let the downloads start
	time.Sleep(500 * time.Millisecond)

	// Now stop the downloader (simulating graceful shutdown)
	t.Log("Stopping downloader...")
	downloader.Stop()
	t.Log("Downloader stopped")

	// Wait for the processing to complete
	wg.Wait()

	// Check that processing completed (even if with errors due to cancellation)
	if processingErr != nil {
		t.Logf("Processing completed with error (expected): %v", processingErr)
	} else {
		t.Log("Processing completed successfully")
	}

	t.Log("Test completed - no hanging goroutines")
}

func TestDownloader_GenerateThumbnailToCache(t *testing.T) {
	mergedColl, failedColl, cleanup := setupDownloaderTest(t)
	defer cleanup()

	// Create test directories
	testDirs := []string{"dir1", "dir2"}
	for _, dir := range testDirs {
		dirPath := filepath.Join("test_data", dir)
		// Remove directory if it exists
		if err := os.RemoveAll(dirPath); err != nil {
			t.Fatalf("Failed to cleanup directory %s: %v", dirPath, err)
		}
		// Create fresh directory
		if err := os.MkdirAll(dirPath, 0755); err != nil {
			t.Fatalf("Failed to create test directory %s: %v", dirPath, err)
		}
	}

	// Create test image
	testImage, err := createTestImage()
	assert.NoError(t, err)

	// Create mock server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "image/jpeg")
		if _, err := w.Write(testImage); err != nil {
			t.Fatalf("Failed to write test image: %v", err)
		}
	}))
	defer server.Close()

	// Create downloader instance
	downloader, err := NewDownloader(&DownloaderOptions{
		Config:       NewDefaultConfig(),
		StoragePaths: []string{filepath.Join("test_data", "dir1"), filepath.Join("test_data", "dir2")},
		MergedCol:    mergedColl,
		FailedCol:    failedColl,
	})
	assert.NoError(t, err)

	tests := []struct {
		name    string
		task    MediaTask
		wantErr bool
		setup   func() error
	}{
		{
			name: "invalid URL",
			task: MediaTask{
				URL:      "invalid-url",
				DestPath: "images/test.jpg",
				MediaKey: "test_key",
			},
			wantErr: true,
		},
		{
			name: "valid task",
			task: MediaTask{
				URL:      server.URL + "/test.jpg",
				DestPath: "images/test.jpg",
				MediaKey: "test_key",
			},
			wantErr: false,
			setup: func() error {
				// Create images directory in each test directory
				for _, dir := range testDirs {
					dirPath := filepath.Join("test_data", dir, "images")
					if err := os.MkdirAll(dirPath, 0755); err != nil {
						return fmt.Errorf("failed to create images directory: %w", err)
					}
				}
				return nil
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Run setup if provided
			if tt.setup != nil {
				if err := tt.setup(); err != nil {
					t.Fatalf("Setup failed: %v", err)
				}
			}

			// Create PropProcessor to test thumbnail caching
			result := &AnalysisResult{
				ID:          "test_prop",
				Sid:         "test_sid",
				NewFirstPic: tt.task,
				OldTnLH:     0,
			}
			processor := &PropProcessor{
				downloader:  downloader,
				result:      result,
				board:       "test",
				cachedFiles: make([]CachedFile, 0),
			}
			hash, _, err := processor.generateThumbnailToCache(tt.task, 0)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, int32(0), hash)
			} else {
				assert.NoError(t, err)
				assert.NotEqual(t, int32(0), hash)
			}
		})
	}
}

func TestDownloader_UpdateMergedDoc(t *testing.T) {
	mergedColl, _, cleanup := setupDownloaderTest(t)
	defer cleanup()

	downloader, err := NewDownloader(&DownloaderOptions{
		Config:    NewDefaultConfig(),
		MergedCol: mergedColl,
	})
	assert.NoError(t, err)

	tests := []struct {
		name          string
		result        *AnalysisResult
		thumbnailHash int32
		board         string
		wantErr       bool
	}{
		{
			name: "successful update without thumbnail",
			result: &AnalysisResult{
				ID:     primitive.NewObjectID().Hex(),
				Sid:    "test123",
				PropTs: time.Now(),
				PhoLH:  []int32{1, 2, 3},
				DocLH:  []string{"4.pdf", "5.pdf", "6.pdf"},
			},
			thumbnailHash: 0,
			board:         "TRB",
			wantErr:       false,
		},
		{
			name: "successful update with thumbnail",
			result: &AnalysisResult{
				ID:     primitive.NewObjectID().Hex(),
				Sid:    "test456",
				PropTs: time.Now(),
				PhoLH:  []int32{1, 2, 3},
				DocLH:  []string{"4.pdf", "5.pdf", "6.pdf"},
			},
			thumbnailHash: 12345,
			board:         "CAR",
			wantErr:       false,
		},
		{
			name: "successful update with different board",
			result: &AnalysisResult{
				ID:     primitive.NewObjectID().Hex(),
				Sid:    "test789",
				PropTs: time.Now(),
				PhoLH:  []int32{7, 8, 9},
				DocLH:  []string{"10.pdf"},
			},
			thumbnailHash: 0,
			board:         "BRE",
			wantErr:       false,
		},
		{
			name: "unset empty fields",
			result: &AnalysisResult{
				ID:     primitive.NewObjectID().Hex(),
				Sid:    "test_unset",
				PropTs: time.Now(),
				PhoLH:  []int32{},  // empty, should be unset
				DocLH:  []string{}, // empty, should be unset
			},
			thumbnailHash: 0, // zero, should be unset
			board:         "TRB",
			wantErr:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Insert a document with the target _id before update
			_, _ = mergedColl.InsertOne(context.Background(), bson.M{"_id": tt.result.ID, "phoLH": []int32{}, "docLH": []string{}})

			err := downloader.updateMergedDoc(tt.result, tt.thumbnailHash, tt.board)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)

				// Verify the document was updated correctly
				var doc bson.M
				err := mergedColl.FindOne(context.Background(), bson.M{"_id": tt.result.ID}).Decode(&doc)
				assert.NoError(t, err)

				// Check phoP field was set
				phoP, exists := doc["phoP"]
				assert.True(t, exists, "phoP field should exist")
				assert.NotEmpty(t, phoP, "phoP field should not be empty")

				// Verify phoP format (should be like "/L1/L2")
				phoPStr, ok := phoP.(string)
				assert.True(t, ok, "phoP should be a string")
				assert.True(t, strings.HasPrefix(phoPStr, "/"), "phoP should start with /")
			}
		})
	}
}

func TestDownloader_UpdateMergedDoc_ErrorCases(t *testing.T) {
	mergedColl, _, cleanup := setupDownloaderTest(t)
	defer cleanup()

	downloader, err := NewDownloader(&DownloaderOptions{
		Config:    NewDefaultConfig(),
		MergedCol: mergedColl,
	})
	assert.NoError(t, err)

	testCases := []struct {
		name    string
		result  *AnalysisResult
		thumb   int32
		board   string
		setup   func(docID string) error
		wantErr bool
	}{
		{
			name: "document_not_found",
			result: &AnalysisResult{
				ID:     "notfound",
				Sid:    "test123",
				PropTs: time.Now(),
				PhoLH:  []int32{1},
				DocLH:  []string{"a.pdf"},
			},
			thumb:   0,
			board:   "TRB",
			setup:   nil,
			wantErr: true,
		},
		{
			name:    "nil_result",
			result:  nil,
			thumb:   0,
			board:   "TRB",
			setup:   nil,
			wantErr: true,
		},
		{
			name: "invalid_board",
			result: &AnalysisResult{
				ID:     primitive.NewObjectID().Hex(),
				Sid:    "test123",
				PropTs: time.Now(),
				PhoLH:  []int32{1},
				DocLH:  []string{"a.pdf"},
			},
			thumb: 0,
			board: "INVALID",
			setup: func(docID string) error {
				_, err := mergedColl.InsertOne(context.Background(), bson.M{"_id": docID, "phoLH": []int32{}, "docLH": []string{}})
				return err
			},
			wantErr: true,
		},
		{
			name: "empty_board",
			result: &AnalysisResult{
				ID:     primitive.NewObjectID().Hex(),
				Sid:    "test123",
				PropTs: time.Now(),
				PhoLH:  []int32{1},
				DocLH:  []string{"a.pdf"},
			},
			thumb: 0,
			board: "",
			setup: func(docID string) error {
				_, err := mergedColl.InsertOne(context.Background(), bson.M{"_id": docID, "phoLH": []int32{}, "docLH": []string{}})
				return err
			},
			wantErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if tc.setup != nil && tc.result != nil {
				_ = tc.setup(tc.result.ID)
			}
			err := downloader.updateMergedDoc(tc.result, tc.thumb, tc.board)
			if tc.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestDownloader_DeleteTask_Comprehensive(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "delete_test")
	require.NoError(t, err)
	defer func() {
		if err := os.RemoveAll(tempDir); err != nil {
			t.Fatalf("Failed to remove test directory: %v", err)
		}
	}()

	// Create test files and directories
	testFile1 := filepath.Join(tempDir, "test1.jpg")
	testFile2 := filepath.Join(tempDir, "test2.jpg")
	testDir := filepath.Join(tempDir, "testdir")

	err = os.WriteFile(testFile1, []byte("test content"), 0644)
	require.NoError(t, err)
	err = os.WriteFile(testFile2, []byte("test content"), 0644)
	require.NoError(t, err)
	err = os.Mkdir(testDir, 0755)
	require.NoError(t, err)

	downloader := &Downloader{
		DownloaderOptions: &DownloaderOptions{
			StoragePaths: []string{tempDir},
		},
	}

	t.Run("successful_deletion", func(t *testing.T) {
		task := DeleteTask{Path: "test1.jpg"}
		err = downloader.deleteTask(task)
		assert.NoError(t, err)

		// Verify file was deleted
		_, err = os.Stat(testFile1)
		assert.True(t, os.IsNotExist(err))
	})

	t.Run("nonexistent_file", func(t *testing.T) {
		task := DeleteTask{Path: "nonexistent.jpg"}
		err = downloader.deleteTask(task)
		assert.NoError(t, err) // Should not error for non-existent files
	})

	t.Run("skip_directory", func(t *testing.T) {
		task := DeleteTask{Path: "testdir"}
		err = downloader.deleteTask(task)
		assert.NoError(t, err)

		// Verify directory still exists
		_, err = os.Stat(testDir)
		assert.NoError(t, err)
	})

	t.Run("multiple_storage_paths", func(t *testing.T) {
		// Create second temp directory
		tempDir2, err := os.MkdirTemp("", "delete_test2")
		require.NoError(t, err)
		defer func() {
			if err := os.RemoveAll(tempDir2); err != nil {
				t.Fatalf("Failed to remove test directory: %v", err)
			}
		}()

		testFile3 := filepath.Join(tempDir2, "test3.jpg")
		err = os.WriteFile(testFile3, []byte("test content"), 0644)
		require.NoError(t, err)

		downloader.StoragePaths = []string{tempDir, tempDir2}
		task := DeleteTask{Path: "test3.jpg"}
		err = downloader.deleteTask(task)
		assert.NoError(t, err)

		// Verify file was deleted from second path
		_, err = os.Stat(testFile3)
		assert.True(t, os.IsNotExist(err))
	})

	t.Run("permission_error", func(t *testing.T) {
		// Create a read-only directory to simulate permission error
		readOnlyDir := filepath.Join(tempDir, "readonly")
		err = os.Mkdir(readOnlyDir, 0444) // Read-only
		require.NoError(t, err)

		// Try to delete a file in read-only directory (this should fail on some systems)
		task := DeleteTask{Path: "readonly/test.jpg"}
		err = downloader.deleteTask(task)
		// This test is system-dependent, so we just ensure it doesn't panic
		// The actual behavior depends on the OS and file system
	})
}

// Note: Comprehensive thumbnail testing is now covered by TestPropProcessor
// which tests the new caching architecture with generateThumbnailToCache

// ========== PropProcessor Tests ==========
// Tests for the new prop-based processing architecture

func TestPropProcessorBasic(t *testing.T) {
	// Create a test downloader with prop worker enabled
	config := NewDefaultConfig()
	config.PropConcurrency = 2

	opts := &DownloaderOptions{
		Config:       config,
		StoragePaths: []string{"/tmp/test_storage"},
	}

	downloader, err := NewDownloader(opts)
	if err != nil {
		t.Fatalf("Failed to create downloader: %v", err)
	}
	defer downloader.Stop()

	// Create a test analysis result
	result := &AnalysisResult{
		ID:  "test_prop_123",
		Sid: "test_sid",
		DownloadTasks: []MediaTask{
			{
				Sid:      "test_sid",
				MediaKey: "test_key_1",
				URL:      "https://httpbin.org/image/jpeg", // Test image URL
				Type:     "photo",
				DestPath: "test/path/image1.jpg",
				IsPhoto:  true,
			},
		},
		DeleteTasks: []DeleteTask{},
		PhoLH:       []int32{12345},
		DocLH:       []string{},
		PropTs:      time.Now(),
		OldTnLH:     0,
		NewFirstPic: MediaTask{
			Sid:      "test_sid",
			MediaKey: "test_key_1",
			URL:      "https://httpbin.org/image/jpeg",
			Type:     "photo",
			DestPath: "test/path/image1.jpg",
			IsPhoto:  true,
		},
	}

	// Test the new prop-based processing
	tnChangedNum, err := downloader.ProcessAnalysisResult(result, "TEST")
	if err != nil {
		t.Logf("ProcessAnalysisResult failed (expected for test): %v", err)
		// This is expected to fail since we don't have real MongoDB collections
		// and the test URL might not be accessible
	} else {
		t.Logf("ProcessAnalysisResult succeeded, tnChangedNum: %d", tnChangedNum)
	}
}

func TestPropProcessorConfiguration(t *testing.T) {
	// Test default configuration
	config := NewDefaultConfig()
	if config.PropConcurrency != 5 {
		t.Errorf("Expected PropConcurrency to be 5, got %d", config.PropConcurrency)
	}

	// Test custom configuration
	config.PropConcurrency = 6

	opts := &DownloaderOptions{
		Config:       config,
		StoragePaths: []string{"/tmp/test_storage"},
	}

	downloader, err := NewDownloader(opts)
	if err != nil {
		t.Fatalf("Failed to create downloader: %v", err)
	}
	defer downloader.Stop()

	// Verify that downloader is created successfully
	// (No worker pools needed in the simplified architecture)
}

func TestCachedFileStructure(t *testing.T) {
	// Test CachedFile structure
	cachedFile := CachedFile{
		Path:        "test/path/file.jpg",
		Data:        []byte("test data"),
		IsPhoto:     true,
		IsThumbnail: false,
	}

	if cachedFile.Path != "test/path/file.jpg" {
		t.Errorf("Expected path 'test/path/file.jpg', got '%s'", cachedFile.Path)
	}
	if string(cachedFile.Data) != "test data" {
		t.Errorf("Expected data 'test data', got '%s'", string(cachedFile.Data))
	}
	if !cachedFile.IsPhoto {
		t.Error("Expected IsPhoto to be true")
	}
	if cachedFile.IsThumbnail {
		t.Error("Expected IsThumbnail to be false")
	}
}

func TestBatchWriteFilesPathBug(t *testing.T) {
	// Test that batchWriteFiles correctly writes each file to its own path
	// This test verifies the bug fix where all files were written to the same path

	// Create temporary directory for testing
	tempDir := t.TempDir()

	// Create downloader with test storage path
	config := NewDefaultConfig()

	opts := &DownloaderOptions{
		Config:       config,
		StoragePaths: []string{tempDir},
	}

	downloader, err := NewDownloader(opts)
	if err != nil {
		t.Fatalf("Failed to create downloader: %v", err)
	}

	// Create PropProcessor with multiple cached files
	pp := &PropProcessor{
		downloader: downloader,
		result: &AnalysisResult{
			ID: "test-prop",
		},
		cachedFiles: []CachedFile{
			{
				Path:    "dir1/file1.jpg",
				Data:    []byte("file1 content"),
				IsPhoto: true,
			},
			{
				Path:    "dir2/file2.jpg",
				Data:    []byte("file2 content"),
				IsPhoto: true,
			},
			{
				Path:    "dir1/file3.pdf",
				Data:    []byte("file3 content"),
				IsPhoto: false,
			},
		},
	}

	// Call batchWriteFiles
	err = pp.batchWriteFiles()
	if err != nil {
		t.Fatalf("batchWriteFiles failed: %v", err)
	}

	// Verify each file was written to the correct path with correct content
	expectedFiles := map[string]string{
		"dir1/file1.jpg": "file1 content",
		"dir2/file2.jpg": "file2 content",
		"dir1/file3.pdf": "file3 content",
	}

	for relativePath, expectedContent := range expectedFiles {
		fullPath := filepath.Join(tempDir, relativePath)

		// Check file exists
		if _, err := os.Stat(fullPath); os.IsNotExist(err) {
			t.Errorf("File %s was not created", fullPath)
			continue
		}

		// Check file content
		actualContent, err := os.ReadFile(fullPath)
		if err != nil {
			t.Errorf("Failed to read file %s: %v", fullPath, err)
			continue
		}

		if string(actualContent) != expectedContent {
			t.Errorf("File %s has incorrect content. Expected: %s, Got: %s",
				fullPath, expectedContent, string(actualContent))
		}
	}
}

func TestBatchWriteFilesDirOptimization(t *testing.T) {
	// Test that directories are created only once per storage path
	// This verifies the optimization where MkdirAll is called only once per unique directory

	tempDir := t.TempDir()

	// Create downloader with optimized config
	config := NewDefaultConfig()
	opts := &DownloaderOptions{
		Config:       config,
		StoragePaths: []string{tempDir},
	}

	downloader, err := NewDownloader(opts)
	if err != nil {
		t.Fatalf("Failed to create downloader: %v", err)
	}

	// Create PropProcessor with multiple files in same directories
	pp := &PropProcessor{
		downloader: downloader,
		result: &AnalysisResult{
			ID: "test-prop",
		},
		cachedFiles: []CachedFile{
			// Multiple files in dir1 - should only create dir1 once
			{Path: "dir1/file1.jpg", Data: []byte("file1"), IsPhoto: true},
			{Path: "dir1/file2.jpg", Data: []byte("file2"), IsPhoto: true},
			{Path: "dir1/file3.jpg", Data: []byte("file3"), IsPhoto: true},
			// Multiple files in dir2 - should only create dir2 once
			{Path: "dir2/file4.jpg", Data: []byte("file4"), IsPhoto: true},
			{Path: "dir2/file5.jpg", Data: []byte("file5"), IsPhoto: true},
			// Nested directories
			{Path: "dir1/subdir/file6.jpg", Data: []byte("file6"), IsPhoto: true},
			{Path: "dir1/subdir/file7.jpg", Data: []byte("file7"), IsPhoto: true},
		},
	}

	// Call batchWriteFiles
	err = pp.batchWriteFiles()
	if err != nil {
		t.Fatalf("batchWriteFiles failed: %v", err)
	}

	// Verify all files were written correctly
	expectedFiles := map[string]string{
		"dir1/file1.jpg":        "file1",
		"dir1/file2.jpg":        "file2",
		"dir1/file3.jpg":        "file3",
		"dir2/file4.jpg":        "file4",
		"dir2/file5.jpg":        "file5",
		"dir1/subdir/file6.jpg": "file6",
		"dir1/subdir/file7.jpg": "file7",
	}

	for relativePath, expectedContent := range expectedFiles {
		fullPath := filepath.Join(tempDir, relativePath)

		// Check file exists
		if _, err := os.Stat(fullPath); os.IsNotExist(err) {
			t.Errorf("File %s was not created", fullPath)
			continue
		}

		// Check file content
		actualContent, err := os.ReadFile(fullPath)
		if err != nil {
			t.Errorf("Failed to read file %s: %v", fullPath, err)
			continue
		}

		if string(actualContent) != expectedContent {
			t.Errorf("File %s has incorrect content. Expected: %s, Got: %s",
				fullPath, expectedContent, string(actualContent))
		}
	}

	// Verify directories were created
	expectedDirs := []string{
		"dir1",
		"dir2",
		"dir1/subdir",
	}

	for _, dir := range expectedDirs {
		fullDirPath := filepath.Join(tempDir, dir)
		if stat, err := os.Stat(fullDirPath); err != nil {
			t.Errorf("Directory %s was not created: %v", fullDirPath, err)
		} else if !stat.IsDir() {
			t.Errorf("Path %s exists but is not a directory", fullDirPath)
		}
	}
}

func TestProcessLogOrder(t *testing.T) {
	// Test that logs are printed in correct order before cached files are cleared
	// This verifies the fix for the bug where cached files were cleared before logging

	tempDir := t.TempDir()

	// Create downloader
	config := NewDefaultConfig()
	opts := &DownloaderOptions{
		Config:       config,
		StoragePaths: []string{tempDir},
	}

	downloader, err := NewDownloader(opts)
	if err != nil {
		t.Fatalf("Failed to create downloader: %v", err)
	}

	// Create a simple prop result for testing
	result := &AnalysisResult{
		ID:            "test-log-order",
		Sid:           "test-sid",
		DownloadTasks: []MediaTask{},
		DeleteTasks:   []DeleteTask{},
		PhoLH:         []int32{},
		DocLH:         []string{},
		PropTs:        time.Now(),
	}

	// Create PropProcessor
	pp := &PropProcessor{
		downloader: downloader,
		result:     result,
	}

	// Mock the download by directly setting cached files
	pp.cachedFiles = []CachedFile{
		{
			Path:    "test/photo.jpg",
			Data:    []byte("test photo data"),
			IsPhoto: true,
		},
		{
			Path:        "test/thumb.jpg",
			Data:        []byte("test thumb data"),
			IsThumbnail: true,
		},
	}

	// Process should complete without errors and log correctly
	result_processed := pp.Process()

	if result_processed.Error != nil {
		t.Fatalf("Process failed: %v", result_processed.Error)
	}

	if result_processed.PropID != "test-log-order" {
		t.Errorf("Expected PropID 'test-log-order', got '%s'", result_processed.PropID)
	}

	// Verify files were written
	expectedFiles := []string{
		"test/photo.jpg",
		"test/thumb.jpg",
	}

	for _, relativePath := range expectedFiles {
		fullPath := filepath.Join(tempDir, relativePath)
		if _, err := os.Stat(fullPath); os.IsNotExist(err) {
			t.Errorf("File %s was not created", fullPath)
		}
	}

	t.Log("Process completed successfully with correct log order")
}

func BenchmarkBatchWriteFiles(b *testing.B) {
	// Benchmark batch write performance with directory optimization
	tempDir := b.TempDir()

	// Create test data (simulate multiple files)
	testFiles := make([]CachedFile, 10)
	for i := 0; i < 10; i++ {
		testFiles[i] = CachedFile{
			Path:    fmt.Sprintf("dir%d/file%d.jpg", i%3, i),
			Data:    make([]byte, 1024*50), // 50KB per file
			IsPhoto: true,
		}
		// Fill with some data
		for j := range testFiles[i].Data {
			testFiles[i].Data[j] = byte(i + j)
		}
	}

	for i := 0; i < b.N; i++ {
		// Create fresh downloader for each iteration
		config := NewDefaultConfig()

		opts := &DownloaderOptions{
			Config:       config,
			StoragePaths: []string{tempDir},
		}

		downloader, err := NewDownloader(opts)
		if err != nil {
			b.Fatalf("Failed to create downloader: %v", err)
		}

		pp := &PropProcessor{
			downloader: downloader,
			result: &AnalysisResult{
				ID: fmt.Sprintf("bench-prop-%d", i),
			},
			cachedFiles: make([]CachedFile, len(testFiles)),
		}

		// Copy test files to avoid data races
		copy(pp.cachedFiles, testFiles)

		// Benchmark the batch write operation
		b.StartTimer()
		err = pp.batchWriteFiles()
		b.StopTimer()

		if err != nil {
			b.Fatalf("batchWriteFiles failed: %v", err)
		}
	}
}

func TestBatchWriteFilesWithSpeedMeter(t *testing.T) {
	// Test that batchWriteFiles correctly tracks write speed using speedmeter
	tempDir := t.TempDir()

	// Create speedmeter
	speedMeter := gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})

	// Create downloader with speedmeter
	config := NewDefaultConfig()
	opts := &DownloaderOptions{
		Config:       config,
		StoragePaths: []string{tempDir},
		SpeedMeter:   speedMeter,
	}

	downloader, err := NewDownloader(opts)
	if err != nil {
		t.Fatalf("Failed to create downloader: %v", err)
	}

	// Create test data
	testData1 := []byte("test file content 1")
	testData2 := []byte("test file content 2 with more data")

	// Create PropProcessor with test files
	pp := &PropProcessor{
		downloader: downloader,
		result: &AnalysisResult{
			ID: "test-prop-speedmeter",
		},
		cachedFiles: []CachedFile{
			{Path: "test1.txt", Data: testData1, IsPhoto: false},
			{Path: "test2.txt", Data: testData2, IsPhoto: false},
		},
	}

	// Call batchWriteFiles
	err = pp.batchWriteFiles()
	if err != nil {
		t.Fatalf("batchWriteFiles failed: %v", err)
	}

	// Check that speedmeter was updated with disk write speeds
	counters := speedMeter.GetCounters()

	// Check that disk write speed was recorded
	var diskSpeedFound bool
	for key := range counters {
		if strings.HasPrefix(key, "writeSpeedMBps_") {
			diskSpeedFound = true
			break
		}
	}
	if !diskSpeedFound {
		t.Errorf("Expected to find disk write speed stats, but none found")
	}

	// Test speedmeter output directly
	stats := speedMeter.ToString(gospeedmeter.UnitS, nil)
	t.Logf("Write speed stats: %s", stats)
}

func TestMultiplePropWriteSpeedTracking(t *testing.T) {
	// Test write speed tracking across multiple props
	tempDir := t.TempDir()

	// Create speedmeter with callback to log stats every 2 props
	// Each prop calls Check twice (writeBytes + propsWritten), so 2 props = 4 calls
	var lastStats string
	speedMeter := gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{
		IntervalTriggerCount: 4,
		IntervalCallback: func(sm *gospeedmeter.SpeedMeter) {
			lastStats = sm.ToString(gospeedmeter.UnitS, nil)
			t.Logf("Speed stats after 2 props: %s", lastStats)
		},
	})

	// Create downloader with speedmeter
	config := NewDefaultConfig()
	opts := &DownloaderOptions{
		Config:       config,
		StoragePaths: []string{tempDir},
		SpeedMeter:   speedMeter,
	}

	downloader, err := NewDownloader(opts)
	if err != nil {
		t.Fatalf("Failed to create downloader: %v", err)
	}

	// Process multiple props with different file sizes
	propData := []struct {
		propID string
		files  []CachedFile
	}{
		{
			propID: "prop-1",
			files: []CachedFile{
				{Path: "prop1/file1.jpg", Data: make([]byte, 1024), IsPhoto: true},
				{Path: "prop1/file2.jpg", Data: make([]byte, 2048), IsPhoto: true},
			},
		},
		{
			propID: "prop-2",
			files: []CachedFile{
				{Path: "prop2/file1.jpg", Data: make([]byte, 512), IsPhoto: true},
				{Path: "prop2/file2.jpg", Data: make([]byte, 1536), IsPhoto: true},
				{Path: "prop2/file3.jpg", Data: make([]byte, 768), IsPhoto: true},
			},
		},
		{
			propID: "prop-3",
			files: []CachedFile{
				{Path: "prop3/file1.jpg", Data: make([]byte, 4096), IsPhoto: true},
			},
		},
	}

	// Process each prop
	for _, prop := range propData {
		pp := &PropProcessor{
			downloader: downloader,
			result: &AnalysisResult{
				ID: prop.propID,
			},
			cachedFiles: prop.files,
		}

		err = pp.batchWriteFiles()
		if err != nil {
			t.Fatalf("batchWriteFiles failed for %s: %v", prop.propID, err)
		}
	}

	// Check final statistics
	finalStats := speedMeter.ToString(gospeedmeter.UnitS, nil)
	t.Logf("Final write speed stats: %s", finalStats)

	// Verify that disk write speed counters exist
	counters := speedMeter.GetCounters()

	// Check that disk write speed was recorded
	var diskSpeedFound bool
	for key, value := range counters {
		if strings.HasPrefix(key, "writeSpeedMBps_") {
			diskSpeedFound = true
			// The counter should have accumulated values from 3 props
			if value <= 0 {
				t.Errorf("Expected disk write speed counter %s to be > 0, got %f", key, value)
			}
			t.Logf("Disk speed counter %s: %f", key, value)
		}
	}
	if !diskSpeedFound {
		t.Errorf("Expected to find disk write speed stats, but none found")
	}
}

func TestPropProcessResult(t *testing.T) {
	// Test PropProcessResult structure
	result := PropProcessResult{
		PropID:        "test_prop_123",
		ThumbnailHash: 12345,
		TnChangedNum:  1,
		Error:         nil,
	}

	if result.PropID != "test_prop_123" {
		t.Errorf("Expected PropID 'test_prop_123', got '%s'", result.PropID)
	}
	if result.ThumbnailHash != 12345 {
		t.Errorf("Expected ThumbnailHash 12345, got %d", result.ThumbnailHash)
	}
	if result.TnChangedNum != 1 {
		t.Errorf("Expected TnChangedNum 1, got %d", result.TnChangedNum)
	}
	if result.Error != nil {
		t.Errorf("Expected Error to be nil, got %v", result.Error)
	}
}

func TestPropProcessorWithOldThumbnail(t *testing.T) {
	// Create test downloader
	downloader, err := NewDownloader(&DownloaderOptions{
		Config:       NewDefaultConfig(),
		StoragePaths: []string{"/tmp/test_storage"},
		MergedCol:    nil, // Will cause failure, but that's expected
		FailedCol:    nil,
	})
	if err != nil {
		t.Fatalf("Failed to create downloader: %v", err)
	}

	// Create test result with old thumbnail
	result := &AnalysisResult{
		ID:  "test_prop_with_old_thumbnail",
		Sid: "test_sid",
		DownloadTasks: []MediaTask{
			{
				Sid:      "test_sid",
				MediaKey: "test_key_1",
				URL:      "https://httpbin.org/image/jpeg",
				Type:     "photo",
				DestPath: "test/path/image1.jpg",
				IsPhoto:  true,
			},
		},
		DeleteTasks: []DeleteTask{}, // Will be populated by thumbnail logic
		PhoLH:       []int32{12345},
		DocLH:       []string{},
		PropTs:      time.Now(),
		OldTnLH:     12345, // Simulate existing old thumbnail
		NewFirstPic: MediaTask{
			Sid:      "test_sid",
			MediaKey: "test_key_1",
			URL:      "https://httpbin.org/image/jpeg",
			Type:     "photo",
			DestPath: "test/path/image1.jpg",
			IsPhoto:  true,
		},
	}

	// Test the prop processing with old thumbnail
	tnChangedNum, err := downloader.ProcessAnalysisResult(result, "TEST")
	if err != nil {
		t.Logf("ProcessAnalysisResult failed (expected for test): %v", err)
		// This is expected to fail since we don't have real MongoDB collections
	} else {
		t.Logf("ProcessAnalysisResult succeeded, tnChangedNum: %d", tnChangedNum)
	}

	t.Logf("Test with old thumbnail completed")
}

// Add additional test cases for edge cases and error conditions
// Example: Test invalid URL, network error, file system error
