package gowatch

import (
	"context"
	"fmt"
	"sync/atomic"
	"time"

	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	gostreaming "github.com/real-rm/gostreaming"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// TokenUpdateOptions contains token update information
type TokenUpdateOptions struct {
	Token          *bson.M     // The new token
	TokenClusterTs time.Time   // Timestamp when token was created
	TokenChangeTs  time.Time   // Timestamp when token was updated
	End            func(error) // Function to end the watch operation
	ResumeMt       time.Time   // Timestamp to resume from
}

// GetToken gets a new token from a collection
func GetToken(col *gomongo.MongoCollection) (*bson.M, time.Time, error) {
	golog.Info("Start getting new token from DB")
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	if col == nil {
		return nil, time.Time{}, fmt.Errorf("collection is nil")
	}

	// Get the client directly
	client := col.Database().Client()
	if client == nil {
		return nil, time.Time{}, fmt.Errorf("failed to get MongoDB client")
	}

	// Create a change stream to get a valid resume token
	opts := options.ChangeStream().SetFullDocument(options.UpdateLookup)
	changeStream, err := col.Watch(ctx, mongo.Pipeline{}, opts)
	if err != nil {
		golog.Error("Failed to create change stream:", err)
		return nil, time.Time{}, err
	}
	defer func() {
		if err := changeStream.Close(ctx); err != nil {
			golog.Error("Error closing change stream:", err)
		}
	}()

	// Get the resume token
	resumeToken := changeStream.ResumeToken()
	if resumeToken == nil {
		// If no resume token is available, create a basic one
		token := &bson.M{
			"_data": bson.M{
				"$binary": bson.M{
					"base64":  primitive.NewObjectID().Hex(),
					"subType": "00",
				},
			},
		}
		tokenClusterTs := time.Now()
		golog.Debug("Created new token", "token", token)
		return token, tokenClusterTs, nil
	}

	// Parse the resume token
	var tokenDoc bson.M
	if err := bson.Unmarshal(resumeToken, &tokenDoc); err != nil {
		golog.Error("Failed to unmarshal resume token:", err)
		return nil, time.Time{}, err
	}

	token := &bson.M{
		"_data": tokenDoc["_data"],
	}
	tokenClusterTs := time.Now()
	golog.Debug("Created new token from resume token", "token", token)
	return token, tokenClusterTs, nil
}

// GetNewTokenAndProcessRemainingDocsOptions contains options for getting new token and processing remaining docs
type GetNewTokenAndProcessRemainingDocsOptions struct {
	FromCol       *gomongo.MongoCollection // Collection to process
	Query         bson.M                   // Query to find remaining documents
	ProcessOneFn  func(bson.M) error       // Function to process each document
	HighWaterMark int                      // Maximum number of documents to process at once
	Context       context.Context          // Context for the operation
	Cancel        context.CancelFunc       // Cancel function for the operation
}

// GetNewTokenAndProcessRemainingDocs gets a new token and processes remaining documents
func GetNewTokenAndProcessRemainingDocs(opt GetNewTokenAndProcessRemainingDocsOptions) (*bson.M, time.Time, error) {
	if opt.HighWaterMark <= 0 {
		opt.HighWaterMark = 1
	}
	if opt.FromCol == nil {
		return nil, time.Time{}, fmt.Errorf("fromCol is required")
	}
	if opt.ProcessOneFn == nil {
		return nil, time.Time{}, fmt.Errorf("processOneFn is required")
	}
	if opt.Context == nil {
		return nil, time.Time{}, fmt.Errorf("context is required")
	}

	token, tokenClusterTs, err := GetToken(opt.FromCol)
	if err != nil {
		return nil, time.Time{}, err
	}

	golog.Info("getNewTokenAndProcessRemainingDocs, query:", opt.Query, "new token:", token)

	// Create cursor with maxTimeMS
	opts := options.Find().SetMaxTime(3 * time.Hour)
	cursor, err := opt.FromCol.Find(opt.Context, opt.Query, opts)
	if err != nil {
		return nil, time.Time{}, err
	}
	defer func() {
		if err := cursor.Close(opt.Context); err != nil {
			golog.Error("error closing cursor:", err)
		}
	}()

	var oldRecordCnt int64
	var processErr error
	var resultToken *bson.M
	var resultTokenClusterTs time.Time

	// Create streaming options
	streamOpts := gostreaming.StreamingOptions{
		Stream: cursor,
		Process: func(item interface{}) error {
			if opt.ProcessOneFn == nil {
				return fmt.Errorf("ProcessOneFn required")
			}
			doc, ok := item.(bson.M)
			if !ok {
				return fmt.Errorf("invalid document type: %T", item)
			}
			atomic.AddInt64(&oldRecordCnt, 1)
			return opt.ProcessOneFn(doc)
		},
		End: func(err error) {
			processErr = err
			resultToken = token
			resultTokenClusterTs = tokenClusterTs
			golog.Info("finish getNewTokenAndProcessRemainingDocs", atomic.LoadInt64(&oldRecordCnt), "records, new token:", token)
		},
		Error: func(err error) {
			golog.Error("Processing error:", err)
		},
		High:    opt.HighWaterMark,
		Verbose: 3,
	}

	// Start streaming
	if err := gostreaming.Streaming(opt.Context, &streamOpts); err != nil {
		return nil, time.Time{}, err
	}

	if processErr != nil {
		return nil, time.Time{}, processErr
	}

	return resultToken, resultTokenClusterTs, nil
}

// UpdateFields contains the fields to update in the system data
type UpdateFields struct {
	Token          *bson.M
	TokenClusterTs time.Time
	TokenChangeTs  time.Time
	Status         string
	ResumeMt       time.Time
}

// UpdateSysDataFunc is the function type for updating system data
type UpdateSysDataFunc func(UpdateFields) error

// GetUpdateSysdataFunction returns a function to update system data
func GetUpdateSysdataFunction(
	SysData *gomongo.MongoCollection,
	SysDataId interface{},
) UpdateSysDataFunc {
	if SysData == nil || SysDataId == nil {
		err := fmt.Errorf("SysData and sysDataId required")
		golog.Error(err.Error())
		return func(fields UpdateFields) error {
			return err
		}
	}

	return func(fields UpdateFields) error {
		// Set default status
		status := fields.Status
		if status == "" {
			status = "running"
		}

		// Prepare update set
		set := bson.M{
			"status": status,
		}

		// Add optional fields
		if !fields.TokenClusterTs.IsZero() {
			set["tokenClusterTs"] = fields.TokenClusterTs
		}
		if !fields.TokenChangeTs.IsZero() {
			set["tokenChangeTs"] = fields.TokenChangeTs
		}
		if fields.Token != nil {
			// Convert token to JSON string
			tokenBytes, err := bson.MarshalExtJSON(*fields.Token, false, false)
			if err != nil {
				golog.Error("Error marshaling token to JSON:", err)
				return err
			}
			set["token"] = string(tokenBytes)
		}
		if !fields.ResumeMt.IsZero() {
			set["resumeMt"] = fields.ResumeMt
		}

		// Prepare update operation
		update := bson.M{"$set": set}
		golog.Debug("updateMongoToken", update)

		// Create a timeout context for the update operation
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		// Execute update with timeout context
		opts := options.Update().SetUpsert(true)
		_, err := SysData.UpdateOne(ctx, bson.M{"_id": SysDataId}, update, opts)
		if err != nil {
			golog.Error("Error updating system data:", err)
		}

		return err
	}
}

// FinishAndUpdateSysdata finishes the watch operation and updates the sysdata
func (w *WatchObject) FinishAndUpdateSysdata(updateSysData UpdateSysDataFunc) error {
	var err error
	// allFinished is a function that updates the system data
	allFinished := func() {
		updateFields := UpdateFields{
			Token:          w.currentToken,
			TokenClusterTs: w.currentTokenClusterTs,
			TokenChangeTs:  w.currentTokenChangeTs,
			Status:         "complete",
		}

		golog.Info("updateSysData in FinishAndUpdateSysdata token:",
			"currentToken", w.currentToken,
			"currentTokenClusterTs", w.currentTokenClusterTs,
			"currentTokenChangeTs", w.currentTokenChangeTs)

		err = updateSysData(updateFields)
		if err != nil {
			golog.Error("Error updating system data", "error", err)
		}
	}

	checkedLoop := 0
	var doCheckAll func()
	// doCheckAll is a function that checks if all processes are finished
	doCheckAll = func() {
		current := atomic.LoadInt32(&w.processingCounter)
		golog.Info("Checking process completion",
			"processingCounter", current,
			"checkedLoop", checkedLoop,
			"ns", w.ns)
		if current <= 0 || checkedLoop > 10 {
			allFinished()
			return
		}
		// check if all processes are finished
		time.AfterFunc(time.Second, func() {
			checkedLoop++
			doCheckAll()
		})
	}

	// start the check
	doCheckAll()

	return err
}
