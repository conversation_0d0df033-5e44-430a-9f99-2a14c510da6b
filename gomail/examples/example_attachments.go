package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"github.com/real-rm/gomail"
)

func main() {
	// Create a sample text file for attachment
	tempDir := os.TempDir()
	attachmentPath := filepath.Join(tempDir, "sample_attachment.txt")
	
	// Create sample attachment file
	err := os.WriteFile(attachmentPath, []byte("This is a sample attachment file content."), 0644)
	if err != nil {
		log.Fatalf("Failed to create sample attachment: %v", err)
	}
	defer os.Remove(attachmentPath) // Clean up after example

	// Create sample image file for embedding
	imagePath := filepath.Join(tempDir, "sample_image.txt")
	err = os.WriteFile(imagePath, []byte("This would be image content in a real scenario."), 0644)
	if err != nil {
		log.Fatalf("Failed to create sample image: %v", err)
	}
	defer os.Remove(imagePath) // Clean up after example

	// Initialize mailer (you would typically load this from config)
	mailer, err := gomail.NewMailer()
	if err != nil {
		log.Fatalf("Failed to create mailer: %v", err)
	}

	// Create email message with attachments
	msg := &gomail.EmailMessage{
		From:    "<EMAIL>",
		To:      []string{"<EMAIL>"},
		Subject: "Test Email with Attachments",
		Text:    "This is a test email with attachments and embedded images.",
		HTML: `<html>
<body>
    <h2>Test Email with Attachments</h2>
    <p>This is a test email with attachments and embedded images.</p>
    <p>You should see an attachment and an embedded image in this email.</p>
    <img src="cid:sample_image.txt" alt="Embedded Image" />
</body>
</html>`,
		Headers: map[string]string{
			"X-Mailer": "GoMail with Attachments",
		},
		// Add file attachments
		Attachments: []string{
			attachmentPath,
		},
		// Add embedded images (referenced in HTML with cid:filename)
		EmbeddedImages: []string{
			imagePath,
		},
	}

	// Send email using Gmail engine
	err = mailer.SendMail("gmail", msg)
	if err != nil {
		log.Fatalf("Failed to send email with attachments: %v", err)
	}

	fmt.Println("Email with attachments sent successfully!")
}
